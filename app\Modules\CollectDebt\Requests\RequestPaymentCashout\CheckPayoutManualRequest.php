<?php

namespace App\Modules\CollectDebt\Requests\RequestPaymentCashout;

use Illuminate\Foundation\Http\FormRequest;

class CheckPayoutManualRequest extends FormRequest
{
	public function authorize()
	{
		return true;
	}

	public function rules()
	{
		return [
			'data' => ['required', 'array'],
			'data.partner_code' => ['required', 'string'],
			'data.partner_request_id' => ['nullable', 'string'],
		];
	}

	public function messages()
	{
		return [];
	}

} // End class
