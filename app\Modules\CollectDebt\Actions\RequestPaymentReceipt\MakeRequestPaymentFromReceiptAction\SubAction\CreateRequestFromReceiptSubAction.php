<?php

namespace App\Modules\CollectDebt\Actions\RequestPaymentReceipt\MakeRequestPaymentFromReceiptAction\SubAction;

use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\DTOs\RequestPayment\CreateRequestPaymentFromReceiptDTO;

class CreateRequestFromReceiptSubAction
{
	public function run(CreateRequestPaymentFromReceiptDTO $dto): RequestPayment
	{
		// For MPOS
		if ($dto->payment_method_code === 'MPOS') {
			return $this->handleMposPayment($dto);
		}

		// For: IB_OFF
		if ($dto->payment_method_code === 'IB_OFF') {
			return $this->handleIboffPayment($dto);
		}

		// For: VIRTUALACCOUNT
		if ($dto->payment_method_code === 'VIRTUALACCOUNT') {
			return $this->handleVirtualAccountPayment($dto);
		}

		// Handle unsupported payment method
		throw new BusinessException('Phương thức thanh toán không được hỗ trợ: ' . $dto->payment_method_code, 5000);
	}

	/**
	 * Handle MPOS payment method
	 */
	private function handleMposPayment(CreateRequestPaymentFromReceiptDTO $dto): RequestPayment
	{
		$requestPayment = RequestPayment::query()->firstWhere([
			'contract_code' => $dto->contract_code,
			'partner_request_id' => $dto->partner_request_id,
		]);

		if (!$requestPayment) {
			throw new BusinessException('Không tìm thấy lệnh trích nợ', 5000);
		}

		if ($requestPayment->status == CollectDebtEnum::RP_STT_PAYMENT_RECEIVED) {
			throw new BusinessException('Lệnh trích nợ đã được nhận tiền. Không thể tạo lệnh trích nợ mới', 5000);
		}

		$dataUpdated = [
			'status_payment' => $dto->status_payment,
			'amount_receiver' =>  $dto->amount_receiver,
			'time_receivered' =>  $dto->time_receivered,
			'receivered_by' => $dto->receivered_by,
			'time_completed' => $dto->time_completed,
			'completed_by' => $dto->completed_by,
			'description' => $dto->description ?? '',
		];

		// Bản thân lệnh trích nợ chưa sang đối tác, thì dùng mã đã sét ở receipt
		if (empty($requestPayment->partner_transaction_id)) {
			$dataUpdated['partner_transaction_id'] = $dto->partner_transaction_id;
		}

		// Nếu lệnh trích chưa hủy thì mới update status này
		if ($requestPayment->status != CollectDebtEnum::RQ_STT_CANCELED) {
			$dataUpdated['status'] = $dto->status;
		}

		$wasUpdated = $requestPayment->update($dataUpdated);

		if (!$wasUpdated) {
			throw new BusinessException('Không thể cập nhật lệnh trích nợ MPOS', 5000);
		}

		return $requestPayment;
	}

	/**
	 * Handle IB_OFF payment method
	 */
	private function handleIboffPayment(CreateRequestPaymentFromReceiptDTO $dto): RequestPayment
	{
		$requestPayment = RequestPayment::query()->forceCreate($dto->toArray());

		if (!$requestPayment) {
			throw new BusinessException('Không thể tạo lệnh trích nợ qua IB_OFF', 5000);
		}
	
		$requestPayment->partner_request_id = sprintf('NLIB%s%s', date('ymd'), $requestPayment->id);
		$r = $requestPayment->save();

		if (!$r) {
			throw new BusinessException('Không thể tạo mã trích nợ cho IB_OFF');
		}

		return $requestPayment;
	}

	/**
	 * Handle VIRTUALACCOUNT payment method
	 */
	private function handleVirtualAccountPayment(CreateRequestPaymentFromReceiptDTO $dto): RequestPayment
	{
		$requestPayment = RequestPayment::query()->firstWhere([
			'partner_request_id' => $dto->partner_request_id,
			'contract_code' => $dto->contract_code,
		]);

		if (!$requestPayment) {
			throw new BusinessException('Không tìm thấy lệnh trích nợ', 5000);
		}

		$wasUpdated = $requestPayment->update([
			'status' => $dto->status,
			'status_payment' => $dto->status_payment,
			'amount_receiver' =>  $dto->amount_receiver,
			'time_receivered' =>  $dto->time_receivered,
			'receivered_by' => $dto->receivered_by,
			'time_completed' => $dto->time_completed,
			'completed_by' => $dto->completed_by,
		]);

		if (!$wasUpdated) {
			throw new BusinessException('Không thể cập nhật lệnh trích nợ VA', 5000);
		}

		return $requestPayment;
	}
} // End class