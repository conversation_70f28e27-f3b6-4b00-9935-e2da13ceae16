\[2025-08-04 10:48:44] local.INFO: API Core ---> func: ColCashoutRequest_getList ---> RawInput:  {"partner_request_ids":["NP0000045566"],"cashout_request_ids":[]} 
[2025-08-04 10:48:44] local.INFO: API Core ---> func: ColCashoutRequest_getList ---> Request:  {"func":"ColCashoutRequest_getList","checksum":"08c64fecb627940bf3167e8c5fcc626b","params":"sA7ZFVyIpG4QyABdrhiyZDWtUzelpZ6a6rK27pfd3F5Ej839F1l7XUrB+/hrYt8z2sdHmx8saGfayYKG3UB2LEIxiaMP9aA91P38exIkTNb802OysXBFf26MKV6/6h1NIGXMb1zDaovzgQd00rFywQ==","username_request":"guest","users_request_id":"1","language":"vi","client_ip":"127.0.0.1"} 
[2025-08-04 10:48:44] local.INFO: API Core ---> func: ColCashoutRequest_getList -----> Response:  {"errorCode":"success","errorDescription":"Thành công","data":"o4vqKkPJarZU0YskQLJv4LhFtOK6smi/VYWRyRSm3qHuOdBK9SCj1hYiNBnHSSqAUiOLiW+qrGvK+EIChrxov6TzhPK08nxwjQGs6d2nJK3GNm8jfN0GmZKeVMiATL/OrTXE05TddBsW0QQKG7Lda9ZRFSicn4273K4XuDMC5xJ/fyq8nji8jTLtY4Rv8YwAVyBeKZwJI4z3ncs/xQGH9+pCZAHSgmdEgOD8qFMdV4txORkCTavW9ZnMgF7mK4U5aOqmz2uwIuTQDl3BCRMb+BaXFTrH0+K0+xOw8Nhmw8SYRyhdOsszTwr+XLiCxkEG7zejEoMqkkMg5OgulWHOtRtqTNAtK+2Sk8ZDQXhGOhSLHgv2KGPaFVvcZPZ1Q1zRRln5F/tgVBy/CCkZqZHrOSwyIgbv7SMfh2Y2s4C/wXIeOjmcKs5baH9h/nG6r3WtJqrzFbHd1eIBoouiTBUsbJ24r9H0TijVi/j7gsOi8uGOFZiv6sejvc8TlSFo8ZQENDshyDNi40VnvuVZN5u6S7xD5/TE3129x8v57ry5KnwRjDZruTFevkzjok8GYZSWcQsx96AQYU8fTmk7QNG9o9COA7o7WhPn89Hwh7XS/ZhNTBZ2to45Sq70uaKdFxTBOvTfE2jX6TuozrjVH+JUibNS7ues7iSuySeoknBUEUSNvSMsa8QBjVwDj65OIFTd319gPoPTWzK9e1/Kz7kwgbulXevxyBFIwd0OoFXtmPCxwHmcOgXR1uaTwhJ2+9Yrmsm8HMQrQLhNTkwJs9dF/KHmx6vnwf1IsTQbQzyb5Aaq+L4dFHUPgauhQQdbR68g0voIr5xBVgH2CyQdudgIF741a2Xv3Uvl98GiPVnbJmQTTpEuXEAJ45PLfxO0SOjvSQThHVzW4C1LNlHp1x4teBCNadRDfC/zbG3+wfS8BMJ9eDuM6JIN7Z2eoL7h8PIKPBguSRkyyqvyNjPD8oPBPHbgFq7KjdOHWfd/36p8JTOrls9M442maxAwyfCJYRV9y51A0NNf8NlxlpaQTw0SevXZu3lV4BggXY2OzT9fM6WQtgMbDStCF4YFZdiAkvRT","remote_ip":"127.0.0.1","language":"vi","function":"ColCashoutRequest_getList","checksum":"47d317549f7a074da390642464bcd1f6"} 
[2025-08-04 10:48:44] local.INFO: DescryptData [{"id":896,"type":6,"account_disbursement":"BIDV","contract_id":1088,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP0000045566","partner_transaction_id":null,"merchant_id":757,"profile_id":****************,"borrower_id":776,"amount_request":1000000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"e1a04c3f6520eccbf2982264bca07230","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null}] 
[2025-08-04 10:50:27] local.INFO: API Core ---> func: ColCashoutRequest_getList ---> RawInput:  {"partner_request_ids":["NP0000045566"],"cashout_request_ids":[]} 
[2025-08-04 10:50:27] local.INFO: API Core ---> func: ColCashoutRequest_getList ---> Request:  {"func":"ColCashoutRequest_getList","checksum":"145cd04b51b56b04265fdc5566bddc35","params":"Qz0c0r13izw0JN2Y0KZ/+IbUYKMvULISESUWd7BFTLfRWwOYe+iC/TDAima0qC4qbSix/yhuSQltlB4+YYNnLNgSCu8TGQQ6mwwBJOnXefmywTNjyojBviwUipruSShBdE2qLGZ+H4aATcUCXiCsDg==","username_request":"guest","users_request_id":"1","language":"vi","client_ip":"127.0.0.1"} 
[2025-08-04 10:50:27] local.INFO: API Core ---> func: ColCashoutRequest_getList -----> Response:   
[2025-08-04 10:50:27] local.ERROR: Lỗi giải mã dữ liệu {"exception":"[object] (App\\Exceptions\\BusinessException(code: 0): Lỗi giải mã dữ liệu at C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Lib\\NextlendCore.php:122)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Actions\\RequestPaymentCashout\\CheckPayoutBulkAction\\CheckPayoutBulkAction.php(58): App\\Lib\\NextlendCore->decryptData()
#1 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Actions\\RequestPaymentCashout\\CheckPayoutManualAction\\CheckPayoutManualAction.php(48): App\\Modules\\CollectDebt\\Actions\\RequestPaymentCashout\\CheckPayoutBulkAction\\CheckPayoutBulkAction->getCashoutDetailBulk(Array)
#2 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Controllers\\RequestPaymentCashout\\RequestPaymentCashoutController.php(46): App\\Modules\\CollectDebt\\Actions\\RequestPaymentCashout\\CheckPayoutManualAction\\CheckPayoutManualAction->run(Object(App\\Modules\\CollectDebt\\Requests\\RequestPaymentCashout\\CheckPayoutManualRequest))
#3 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Modules\\CollectDebt\\Controllers\\RequestPaymentCashout\\RequestPaymentCashoutController->CheckPayoutManual(Object(App\\Modules\\CollectDebt\\Requests\\RequestPaymentCashout\\CheckPayoutManualRequest))
#4 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('CheckPayoutManu...', Array)
#5 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(239): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\CollectDebt\\Controllers\\RequestPaymentCashout\\RequestPaymentCashoutController), 'CheckPayoutManu...')
#6 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(196): Illuminate\\Routing\\Route->runController()
#7 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#8 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Http\\Middleware\\CheckTokenMiddleware.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckTokenMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#15 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#17 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\public\\index.php(57): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 {main}
"} 
[2025-08-04 10:50:31] local.INFO: API Core ---> func: ColCashoutRequest_getList ---> RawInput:  {"partner_request_ids":["NP0000045566"],"cashout_request_ids":[]} 
[2025-08-04 10:50:31] local.INFO: API Core ---> func: ColCashoutRequest_getList ---> Request:  {"func":"ColCashoutRequest_getList","checksum":"4aaab528a2977a572e15788b86ced1b5","params":"ux2vF/S8wDY3A7h1yzBROFcBuD6t3dLalCEExMDEeG3GFv3u9KVFPsnEe1Kz5ku2bMaTA4Qck4FMDRLQhzNbKdByORMf3OqLVq53HUnzIMiwMYM8CkJeZ8/ycupAFFNaE/ceog8Fne0NOERMqbfJtQ==","username_request":"guest","users_request_id":"1","language":"vi","client_ip":"127.0.0.1"} 
[2025-08-04 10:50:31] local.INFO: API Core ---> func: ColCashoutRequest_getList -----> Response:   
[2025-08-04 10:50:31] local.ERROR: Lỗi giải mã dữ liệu {"exception":"[object] (App\\Exceptions\\BusinessException(code: 0): Lỗi giải mã dữ liệu at C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Lib\\NextlendCore.php:122)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Actions\\RequestPaymentCashout\\CheckPayoutBulkAction\\CheckPayoutBulkAction.php(58): App\\Lib\\NextlendCore->decryptData()
#1 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Actions\\RequestPaymentCashout\\CheckPayoutManualAction\\CheckPayoutManualAction.php(48): App\\Modules\\CollectDebt\\Actions\\RequestPaymentCashout\\CheckPayoutBulkAction\\CheckPayoutBulkAction->getCashoutDetailBulk(Array)
#2 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Controllers\\RequestPaymentCashout\\RequestPaymentCashoutController.php(46): App\\Modules\\CollectDebt\\Actions\\RequestPaymentCashout\\CheckPayoutManualAction\\CheckPayoutManualAction->run(Object(App\\Modules\\CollectDebt\\Requests\\RequestPaymentCashout\\CheckPayoutManualRequest))
#3 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Modules\\CollectDebt\\Controllers\\RequestPaymentCashout\\RequestPaymentCashoutController->CheckPayoutManual(Object(App\\Modules\\CollectDebt\\Requests\\RequestPaymentCashout\\CheckPayoutManualRequest))
#4 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('CheckPayoutManu...', Array)
#5 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(239): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\CollectDebt\\Controllers\\RequestPaymentCashout\\RequestPaymentCashoutController), 'CheckPayoutManu...')
#6 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(196): Illuminate\\Routing\\Route->runController()
#7 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#8 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Http\\Middleware\\CheckTokenMiddleware.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckTokenMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#15 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#17 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\public\\index.php(57): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 {main}
"} 
[2025-08-04 10:50:33] local.INFO: API Core ---> func: ColCashoutRequest_getList ---> RawInput:  {"partner_request_ids":["NP0000045566"],"cashout_request_ids":[]} 
[2025-08-04 10:50:33] local.INFO: API Core ---> func: ColCashoutRequest_getList ---> Request:  {"func":"ColCashoutRequest_getList","checksum":"f8176ed8b50a1313638f5acf0988f262","params":"nnkZZNw4R0/CVN2Y6zf8qNU/flGfsDXLI6dH1uZH2/KEOJzx9Pjwli2xs9JNnRzi45ycejuypeX38fddbfkjTZ78U7VimIqq9O1ONZtM8c3l9Ndrg99ucj3ex0E1zxk2TPo0ghA3Mr5xru2IjAmQIw==","username_request":"guest","users_request_id":"1","language":"vi","client_ip":"127.0.0.1"} 
[2025-08-04 10:50:33] local.INFO: API Core ---> func: ColCashoutRequest_getList -----> Response:  {"errorCode":"success","errorDescription":"Thành công","data":"+LlV6U0TRQ968iYJdB6wnueU845rQrfWta4K5HRK6Y5sOG73X+hyop72OaRQ17wz26sdr96XNaxiyvF9zvB4T1b8+i34+wLJzZQcF3zCGdX3HoHwPCX1i49LTlw1yLkTuyKC652dtZZvkx+SIkXLYFOa6aZtSicbZ86utQO50lcK+JDLqJkGa+japYPG0xHrOJkyMnhAxctocogp2Er8xnMISLcRIzH8vSA8wZm6Zg+ar/7FEPqgmV8OmUoupP9iEFj2h6Bs6cVLWHRG03dSJrC1GNx5T36jG09ornuihofg8m8vCa5nqz/ZbgyDn3SO/zUE4CpwDIqrSGjDMbp/ySo3PJiWsS5xYmmsLXKcuPCuq72C7t0D9nCBSriasfWQODgULn0GtX931VbdsKWXu4vPspVAcguxMiCCqakj2Uktfl+wyZKlcDOEaWIxGvrZySu6FlzrLWy9xzuJ1VOjx7fVMo+Rrlv9PgQmEEKI0t2h9RguLBw59Qfg+5cpaahYmXyGDCPhJxXKwqbkmxK0yeC8VUvpJ8rxQ6WBpAN6CgNOmgUcdrD1p9XgUcawuSWyPdbQZhUpa/tMK5t+2CpNksWKzl0wLh5y5umZ3+XgHBD6NymrhtJaef2cdNKk5/OiabehhubEC5Wi+yyheL7Tl7yn0Rjf5Sne31SEaBbe14PyoZT5ahc51zklgrhUz9LDBIdI09C40xGnrpg4XXH3Bkbte5aorP9+yf+wGjzybxieIZrd12Lay6Jt6byY0xvUHs/qQfMX1NkaIuNL6GUj1kY40bJyHnkhOfYtzBxqrT7zUEZBieRe1YYxWdQZuZ41MagrtDb0m6v1UJpCWAYD+BZ2dhqEG5VPNsFw7bMVtzTj/PtXJR7249w7i9aaKHQcIGtg4bzswd2i8exFszKjW9BPQ8V9uKbHHFFW5LuXctJp/2cs+d6pLFNVIyy8rUMhpo7l3AbEQMzgUUwWEkvYPkcbN5ewxpQt27XLaZ+i4VnTcbYq7VAkUDy78cMQXe0bFcXhJVpXqTo00JSe+IUIZ/T8LZe+oH6Io8gQOAlbF13FAadaw1Aj++qZRnHylcRt","remote_ip":"127.0.0.1","language":"vi","function":"ColCashoutRequest_getList","checksum":"8da7658536ca7ff85c37cce6ef17d6c7"} 
[2025-08-04 10:50:33] local.INFO: DescryptData [{"id":896,"type":6,"account_disbursement":"BIDV","contract_id":1088,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP0000045566","partner_transaction_id":null,"merchant_id":757,"profile_id":****************,"borrower_id":776,"amount_request":1000000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"e1a04c3f6520eccbf2982264bca07230","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null}] 
[2025-08-04 10:51:10] local.INFO: API Core ---> func: ColCashoutRequest_getList ---> RawInput:  {"partner_request_ids":["NP0000045566"],"cashout_request_ids":[]} 
[2025-08-04 10:51:10] local.INFO: API Core ---> func: ColCashoutRequest_getList ---> Request:  {"func":"ColCashoutRequest_getList","checksum":"47b49300551b489cdd35ae903fa041f5","params":"/9qVII0PWLMXkp9el8/SsVS5p+T8dmR5VUugdmteByt2KfBft5Js8AKr1XmsNEmvCYKM6Ot/9sQAbgb2cu4S0OiI5/uYGlpEPM8hZma1YAcRocfXYLKAvazCClKgMvhkbbQFOhlD/16uY9gpDJ9SoQ==","username_request":"guest","users_request_id":"1","language":"vi","client_ip":"127.0.0.1"} 
[2025-08-04 10:51:10] local.INFO: API Core ---> func: ColCashoutRequest_getList -----> Response:  {"errorCode":"success","errorDescription":"Thành công","data":"CEoJ6930GbJSnpMaJwwRAfnZh7PRW8BJjpJjvklRFq5iFKkJiWuCLVuutFzdR4fnacR02ntDDW08JO31sY12KkNcae1uAiBzozaZK+c1SafUr3gQe9+V46iKfPG23n+aBMcATq6/swzS+/Qi4SYQbfmzGdQTjvwHXnUql9OPpq9k8/27pQ2q4rmEDeaXYWdn35XKO7QeNriDtcjANK8vI6XnfqVjPVClYDhRzHy5igFjdJgZi2JuVr1+WDwUr25TDsk1GgoKI8nyWixBeQukP43zKZ4qfgS0LXv0U5CYEu0dnBlUzvshx/wjyTerbPyl6o/8Wksy7qgJVlu2Ck5rz4n8Nul5n2PvpQyyo+Y6O/1QgAtwcD0gNu6A7vEy4FYzgQSYesxiQ0CVWqM3SfDuAbhheAJvzQ0CXkatxP8m7FUqLXmNnPxsJIUGL30wWsczHLtk+Xjss8Ly5QJDeNvKCLYBuIMchZJ1twZQRo9BO95K2NnGJg1Srgd1JnoVshlGIye5/A1fexnWNUsWjEkcPGn2MbXIi9wrK6OYrPPTtDena3WDFXqc3s6GtvVRx24D8WcMiJrQV7YRpezSJeHrpZxaa8X7URHEgFu9gdJQs2nnSXmZuxV0+5er0Huxn52rWwfBoduogur6+2rc4VBA8Dmuqr0rLc3yFHlsRGgk6wJ3jwJiO+fq/iZqVLvdR3WmU0yJcJqbHeij4UMh3qTYj5c5qGk6RWSgiZqUnlHyNthZS6f4TzINmKX+7kO+FnWAR4RCW/kOiJBOhsLYxNCA/9HZ5tCQ0gQ7Wk4s4g9eO1/HDzFtZcgITw2vWLsVksL2ERtzrNm5Zv8Pqo2wkAaQz4zQedjzkidPMKPbnqpri36CQOy9/5ci6HoM0dyu69znq575SOORoI8RaW53Ay5phTx1Y4oV0cX9ziXEsdknMocs/DURaYx1fbN9Ov5WloJi/UcwJiqPsCUJGZMUt0e6j1glcThJmMaDUA9kwYzCiD/fH5hUQOezmSpXzAFcz+zLtpgNaQfoV8GhdOFZ0JkwpAFMq1kIa9ZAliYYFoU2Uh9vcdMB+D3ZHJPNzrGIAZif","remote_ip":"127.0.0.1","language":"vi","function":"ColCashoutRequest_getList","checksum":"051a7a1943a8c373cb17786ce874633e"} 
[2025-08-04 10:51:10] local.INFO: DescryptData [{"id":896,"type":6,"account_disbursement":"BIDV","contract_id":1088,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP0000045566","partner_transaction_id":null,"merchant_id":757,"profile_id":****************,"borrower_id":776,"amount_request":1000000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"e1a04c3f6520eccbf2982264bca07230","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null}] 
[2025-08-04 10:56:02] local.INFO: API Core ---> func: ColCashoutRequest_getList ---> RawInput:  {"partner_request_ids":["NP0000045566"],"cashout_request_ids":[]} 
[2025-08-04 10:56:02] local.INFO: API Core ---> func: ColCashoutRequest_getList ---> Request:  {"func":"ColCashoutRequest_getList","checksum":"22799fc21e21faa35f63ef6107e72a36","params":"QHzinC1w1UEcLxQsCTak0mIysb2DLZxRYOTZ+0R9CSgOelux/tQoof3SW1q+Aa0Rk33NprNJpxfsbN66CE0HBCOPgTB2HcVXOnRayW5e5/MoPnZ2c331xZEcM8jJGA9Rc8RJjsmuQX81A/EYmuvxog==","username_request":"guest","users_request_id":"1","language":"vi","client_ip":"127.0.0.1"} 
[2025-08-04 10:56:02] local.INFO: API Core ---> func: ColCashoutRequest_getList -----> Response:  {"errorCode":"success","errorDescription":"Thành công","data":"o7RRYppna6yW52xgT/GyfKnlBAdjEZ/fqKcyWG/I6KL8cjGQR/IVOU5TqRoKmZlv/EyBmjDXSHox1hK/glAFwJWYsDpTlyq7PH5fpvZL8sl0d6dMSXQKdr0ccxV52vyWq18NC7PwH+u6XYWFNJFTt2BeIfve/OZmH74cunFeMED9iyCExYlRzl8sHuKnfAMoOUgotuDK/I54YCW0ky/JsbHlnu2WUdNUesIgQaOieKAiDunI/qomjQNS/Ts/lM6y+CdtU0HcS/v22+OWm+TSlqmqCqaRPNAs3tbj3Kbs9CHvBMQu+6FpJzm4DKCqAI46HcCsRjAnqqNg8gXbnc8oQesalaQrUwo/YlC6z4MYxvA3J54LhnqqoEVaDOQznO/8uOEYzfeEBIKzVfrY5FPXotA2fKa1+yxTlXOJR6otnAKS8kE9He9zdKsl75zRL4fIEdDq9twT7WPZpMIc0voX/wdPAhEnXoc5JcqLKhk3OU8QujIqYzM6ameU9zzUwehgrJj4qWjB+6/hT4CYO/6pJ5UEpKdUPR+A4xA1d6cecnX4bPDOd9z8EcqwljB0DdisLYk+jBMBS9Uau8hwsCaVPVHm+QZgGcVgBrTKlZJrH+j6uNdX+GalcJ2iGlcb6CspfUzjVzhV1474nocFcEeQFEulhpYt7/heEay0Noxs5Z442nP/NRNvtCqc1OBWXIuansBr9A1ZZoid80TW+YwcoF3zpDz4/wveXVmuF2HwJadoMkZ0ED9qRN+WHHhBhZOPUplYVtSsTYHjHhvfujs6OVImmRnjnOPKcrc3cmgzARXvEwggPxStQavGU9JFlow4C+gJYHh00A1iHgpnDYTWBL0j83n7rF+tcpKX4PftcH/A7bnK0gfJ7jiGI7/2+1sLFlG+AbG9ZMUAzT8iskBOndmnhgiGCHdrUWOYd7bweWdWFtuxY7AR8Bume8plECiGRK8A4KpWD3MYMlnNwuPP+yz6Gxt1McfvfYJJSPK6zwyzQUvfOunjJcXgTbxZypQGjRvHZ2Vw+8/qa5FDdj/7lnn4SbhkfYc7LoW335QEmnkVfutYDS0Svo2cC1y7v/U4","remote_ip":"127.0.0.1","language":"vi","function":"ColCashoutRequest_getList","checksum":"24ce4a508aac034c5ac3010baaf45008"} 
[2025-08-04 10:56:02] local.INFO: DescryptData [{"id":896,"type":6,"account_disbursement":"BIDV","contract_id":1088,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP0000045566","partner_transaction_id":null,"merchant_id":757,"profile_id":****************,"borrower_id":776,"amount_request":1000000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"e1a04c3f6520eccbf2982264bca07230","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null}] 
[2025-08-04 11:04:58] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '' for key 'request_payment_receipt.partner_transaction_id' (SQL: insert into `request_payment_receipt` (`contract_code`, `partner_transaction_id`, `partner_request_id`, `payment_method_code`, `payment_channel_code`, `payment_account_id`, `amount`, `time_created`, `created_by`, `description`, `status`, `request_payment_guide_id`, `partner_code`) values (BIDV-*************-L1, , NLMPOS250801302, MPOS, MPOS, *********, 0, **********, system, Cutoff lệnh trích quá khứ không sang được đối tác trích nợ, 4, 124, BIDV)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '' for key 'request_payment_receipt.partner_transaction_id' (SQL: insert into `request_payment_receipt` (`contract_code`, `partner_transaction_id`, `partner_request_id`, `payment_method_code`, `payment_channel_code`, `payment_account_id`, `amount`, `time_created`, `created_by`, `description`, `status`, `request_payment_guide_id`, `partner_code`) values (BIDV-*************-L1, , NLMPOS250801302, MPOS, MPOS, *********, 0, **********, system, Cutoff lệnh trích quá khứ không sang được đối tác trích nợ, 4, 124, BIDV)) at C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:671)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('insert into `re...', Array, Object(Closure))
#1 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(465): Illuminate\\Database\\Connection->run('insert into `re...', Array, Object(Closure))
#2 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(417): Illuminate\\Database\\Connection->statement('insert into `re...', Array)
#3 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `re...', Array)
#4 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2839): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `re...', Array, 'id')
#5 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1422): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(902): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(867): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(730): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(449): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(433): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Modules\\CollectDebt\\Model\\RequestPaymentReceipt))
#11 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(450): tap(Object(App\\Modules\\CollectDebt\\Model\\RequestPaymentReceipt), Object(Closure))
#12 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Actions\\RequestPaymentReceipt\\CreateRequestPaymentReceiptAction\\CreateRequestPaymentReceiptAction.php(16): Illuminate\\Database\\Eloquent\\Builder->firstOrCreate(Array, Array)
#13 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Actions\\RequestPayment\\CutOffRequestPaymentAction\\CutOffRequestPaymentAction.php(86): App\\Modules\\CollectDebt\\Actions\\RequestPaymentReceipt\\CreateRequestPaymentReceiptAction\\CreateRequestPaymentReceiptAction->run(Object(App\\Modules\\CollectDebt\\DTOs\\RequestPaymentReceipt\\CreateRequestPaymentReceiptDTO))
#14 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Actions\\RequestPayment\\CutOffRequestPaymentAction\\CutOffRequestPaymentAction.php(113): App\\Modules\\CollectDebt\\Actions\\RequestPayment\\CutOffRequestPaymentAction\\CutOffRequestPaymentAction->handleEmptyPartnerTransactionId(Object(App\\Modules\\CollectDebt\\Model\\RequestPayment))
#15 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Actions\\RequestPayment\\CutOffRequestPaymentAction\\CutOffRequestPaymentAction.php(71): App\\Modules\\CollectDebt\\Actions\\RequestPayment\\CutOffRequestPaymentAction\\CutOffRequestPaymentAction->processMposPayment(Object(App\\Modules\\CollectDebt\\Model\\RequestPayment))
#16 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Actions\\RequestPayment\\CutOffRequestPaymentAction\\CutOffRequestPaymentAction.php(43): App\\Modules\\CollectDebt\\Actions\\RequestPayment\\CutOffRequestPaymentAction\\CutOffRequestPaymentAction->processCutOffRequestPayment()
#17 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Controllers\\RequestPayment\\RequestPaymentController.php(58): App\\Modules\\CollectDebt\\Actions\\RequestPayment\\CutOffRequestPaymentAction\\CutOffRequestPaymentAction->run()
#18 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Modules\\CollectDebt\\Controllers\\RequestPayment\\RequestPaymentController->CutOffRequestPayment()
#19 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('CutOffRequestPa...', Array)
#20 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(239): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\CollectDebt\\Controllers\\RequestPayment\\RequestPaymentController), 'CutOffRequestPa...')
#21 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(196): Illuminate\\Routing\\Route->runController()
#22 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#23 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#42 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\public\\index.php(57): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#43 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '' for key 'request_payment_receipt.partner_transaction_id' at C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:464)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(464): PDOStatement->execute()
#1 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(664): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into `re...', Array)
#2 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('insert into `re...', Array, Object(Closure))
#3 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(465): Illuminate\\Database\\Connection->run('insert into `re...', Array, Object(Closure))
#4 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(417): Illuminate\\Database\\Connection->statement('insert into `re...', Array)
#5 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `re...', Array)
#6 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2839): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `re...', Array, 'id')
#7 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1422): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#8 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(902): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(867): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(730): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(449): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(433): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Modules\\CollectDebt\\Model\\RequestPaymentReceipt))
#13 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(450): tap(Object(App\\Modules\\CollectDebt\\Model\\RequestPaymentReceipt), Object(Closure))
#14 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Actions\\RequestPaymentReceipt\\CreateRequestPaymentReceiptAction\\CreateRequestPaymentReceiptAction.php(16): Illuminate\\Database\\Eloquent\\Builder->firstOrCreate(Array, Array)
#15 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Actions\\RequestPayment\\CutOffRequestPaymentAction\\CutOffRequestPaymentAction.php(86): App\\Modules\\CollectDebt\\Actions\\RequestPaymentReceipt\\CreateRequestPaymentReceiptAction\\CreateRequestPaymentReceiptAction->run(Object(App\\Modules\\CollectDebt\\DTOs\\RequestPaymentReceipt\\CreateRequestPaymentReceiptDTO))
#16 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Actions\\RequestPayment\\CutOffRequestPaymentAction\\CutOffRequestPaymentAction.php(113): App\\Modules\\CollectDebt\\Actions\\RequestPayment\\CutOffRequestPaymentAction\\CutOffRequestPaymentAction->handleEmptyPartnerTransactionId(Object(App\\Modules\\CollectDebt\\Model\\RequestPayment))
#17 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Actions\\RequestPayment\\CutOffRequestPaymentAction\\CutOffRequestPaymentAction.php(71): App\\Modules\\CollectDebt\\Actions\\RequestPayment\\CutOffRequestPaymentAction\\CutOffRequestPaymentAction->processMposPayment(Object(App\\Modules\\CollectDebt\\Model\\RequestPayment))
#18 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Actions\\RequestPayment\\CutOffRequestPaymentAction\\CutOffRequestPaymentAction.php(43): App\\Modules\\CollectDebt\\Actions\\RequestPayment\\CutOffRequestPaymentAction\\CutOffRequestPaymentAction->processCutOffRequestPayment()
#19 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Controllers\\RequestPayment\\RequestPaymentController.php(58): App\\Modules\\CollectDebt\\Actions\\RequestPayment\\CutOffRequestPaymentAction\\CutOffRequestPaymentAction->run()
#20 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Modules\\CollectDebt\\Controllers\\RequestPayment\\RequestPaymentController->CutOffRequestPayment()
#21 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('CutOffRequestPa...', Array)
#22 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(239): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\CollectDebt\\Controllers\\RequestPayment\\RequestPaymentController), 'CutOffRequestPa...')
#23 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(196): Illuminate\\Routing\\Route->runController()
#24 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#25 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\public\\index.php(57): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 {main}
"} 
[2025-08-04 11:33:25] local.INFO: ParamGuiLenhTrich: {"nextlend_request_id":303,"merchantId":"*********","lendingRequestId":"NLMPOS250804303","debitAmount":1000000.0,"requestTime":"20250804113325","lendingId":"BIDV-*************-L1","loanOriginalAmount":1000000.0,"deductionPerDayAmount":1000000.0,"loanBalance":1000000.0,"partner_code":"MPOS","partnerCode":"NEXT_LEND","_prefix":"THUHO","recoverEndTime":"20250804230000999","recoverStartTime":"20250804113256000"} 
[2025-08-04 11:33:25] local.INFO: sendDebtInput {"Fnc":"sendDebt","Checksum":"1ee4094be3940ef2cf3146b98d53ba44","EncData":"E3+g567mWs7m+68VRQfpbbXZ0gbb4VtVAOd5rVNT5T9OAuQaFqPdQUoq1yX+Dzj0/2GLpgbFJ+ayItrsSP7vhSS+npQXMzUQ1Exfqc7DLKOnBnY3diJcFmaUoKT+x6/hh8/9vViSoWs3SH/kceaJjWwosk8IooX9MGGyLKiljS5Lf9jDT527gvoRR27iDZb3lT86mgRpZ8Q1cnWOFxc8746wxrMaxLcccVTVfUuRRU7ItRkvAJC0RsBhxrCpY1auDLPj+zMliWOPzL2np+pIdj6ivAQIQ/UPk+Jo3u7ZC3D+rfjUcobpkWl30wbZn6RXXCWjma6xfe+bt78dG+d4TsP1XON85uIG/HKLTkrrfK/TNrLugWeqsjMpeDqiTGgl/noFD3nS0xMKbSkHF10kNt7kLk+/e0+KX0bkKJcJxJ9R+Li/Y/KGBKXO7COkKjyPq2DixYsRyfFgTOMjSDWPEp/WxnVY0hbWK1As2hENGl7C4R8hNr51HIJNBNB6Ga8ohtsVQ2R8GHsqg5y7tO6EIxq0vNu+30G+wzBuTaf8/CI/yYh+krnMFJ9bTg7isPpU","ChannelCode":"THUHO","Version":"1.0"} 
[2025-08-04 11:33:26] local.INFO: sendDebtResponse: {"Fnc":"sendDebt","Version":"1.0","ChannelCode":"THUHO","RespCode":3001,"data":"{\"errorCode\":3001,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":3001,\\\"message\\\":\\\"Mpos user's password not matched\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"6d86297c5267736285e78455fa130686","Description":"ERROR"}  
[2025-08-04 11:33:26] local.INFO: ResultGuiTrichMpos {"errorCode":3001,"data":{"status":false,"error":{"code":3001,"message":"Mpos user's password not matched","messageEn":""}}} 
[2025-08-04 12:05:03] local.ERROR: Loi khong update trang thai gui lenh thanh dang gui {"exception":"[object] (App\\Exceptions\\BusinessException(code: 0): Loi khong update trang thai gui lenh thanh dang gui at C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Actions\\RequestPayment\\SendRequestPaymentAction\\SendRequestPaymentAction.php:69)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Actions\\RequestPayment\\SendRequestPaymentAction\\SendRequestPaymentAction.php(25): App\\Modules\\CollectDebt\\Actions\\RequestPayment\\SendRequestPaymentAction\\SendRequestPaymentAction->handlePaymentSending()
#1 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Controllers\\RequestPayment\\RequestPaymentController.php(46): App\\Modules\\CollectDebt\\Actions\\RequestPayment\\SendRequestPaymentAction\\SendRequestPaymentAction->run()
#2 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Modules\\CollectDebt\\Controllers\\RequestPayment\\RequestPaymentController->SendRequestPayment()
#3 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('SendRequestPaym...', Array)
#4 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(239): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\CollectDebt\\Controllers\\RequestPayment\\RequestPaymentController), 'SendRequestPaym...')
#5 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(196): Illuminate\\Routing\\Route->runController()
#6 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#7 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\public\\index.php(57): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 {main}
"} 
[2025-08-04 12:05:03] local.INFO: ParamGuiLenhTrich: {"nextlend_request_id":303,"merchantId":"*********","lendingRequestId":"NLMPOS250804303","debitAmount":1000000.0,"requestTime":"20250804120503","lendingId":"BIDV-*************-L1","loanOriginalAmount":1000000.0,"deductionPerDayAmount":1000000.0,"loanBalance":1000000.0,"partner_code":"MPOS","partnerCode":"NEXT_LEND","_prefix":"THUHO","recoverEndTime":"20250804230000999","recoverStartTime":"20250804113256000"} 
[2025-08-04 12:05:03] local.INFO: sendDebtInput {"Fnc":"sendDebt","Checksum":"8fd2deb095f381be7fd013c46a8a9af2","EncData":"Nxvqj7MX9LBk3ff44j/uQ3ieowKeaJ7d1xanrKGRQiL9uZquy/B6CTO+yqdJ0FDd58mtggGDo+WHtC7Vi1OWcPkm5eLDi94KMek7KClQvmodJr1Jp2CsnSVnDYyng8Y96/qY0EJ604KJqUVi3Qmbe7CDBGxDBhX1vbY31jkLQD9qLxsLRsodoZafe8S3pBu7VVxkl60/kquAKNAkTINpl+eo648hf1JQ5iX1BoYbKbCf6xGmIDV0DO8ZFzuriXtXqHtYLB4il9R46d33Z3g3cjf/gl7vj4+iixetxdAJ82FCTbFqw1wyUaofUIe4E9+EBq0lOPjz4PoVub9x0N0n0VtIBAJ1BTo1bzEkplb5I4hpteHhI9gOTjdMzeyhHwCUmZIQtFAiqBc1Gnvgd7ZDZqgWUdq1RSThkEp9TC6FcNV7tsG2Y1aQflDPC8M5qDwoplowDH+k5NdkRcxgJSzow6M2FOFSjxUmZCPIoTKlcMhcOvvVjSBWJjASskJu8VwWROjajTzT8ER93lOPzEYUQ88WU+jZ6RIXVTuKLlxGYN8nCggF/nCIJV/Sp7fwtqBJ","ChannelCode":"THUHO","Version":"1.0"} 
[2025-08-04 12:05:04] local.INFO: API Core ---> func: ColCashoutRequest_getList ---> RawInput:  {"partner_request_ids":["NP288877773","NP288877776633","***************","NP000002","NP000003","NP000004","NP000006","NP000007","NP010010101","NP0100101012","NP00000145","NP00000147","NP00000133","NP00000154","NP00000445","NP00000447","NP0000045566","NLTC1334464","NLTC133444564","NLTC13367664","NLTC1767664","NLTC176767664","NP00033346","NLTC17655657764"],"cashout_request_ids":[]} 
[2025-08-04 12:05:04] local.INFO: API Core ---> func: ColCashoutRequest_getList ---> Request:  {"func":"ColCashoutRequest_getList","checksum":"abae055e14c4520f55e355ed17f998ae","params":"lyN2UcVQ/z12VkSer3aQRlA3MxH3l7wyZQwOzn7SSn464YIwvTvbLRaqH/6oCwJM4k8rV9COq9XEPuqGk0wzdZntpWH9+wyJ1IKVl0M+bsGu7jMxcTvm0DKp2Yfz7PiUX5+SBBpPybuyTAC2CHsYPu7Ndujco/SvxxYiqgTGygavpf89uIcq+5QMaUVX7J+1aDAlgWcsCijHBkmA5HhGtSOS/bk4Z758AjOCUzx+411UBorU8yo2hYdPka2jr1i5brMh2j37KsOA2DqQgYSwCOosdZJa4FQbodOUEwhHFHVBIG/erguCq6APKLmPBFF+0DAFJ0luJWS+LCj7K6DhMHF0L1Hx/ctWuGV3TshZT6xkb16EFWsSSZ3uOYtTRCjI8EKUfIIOeuu5XDhRdRp5QKkZHxz/W9n3uYWQ1g+pfwf4AeEfvqMsfWiCl6fwHvl8SF7FEHceeBV8c75Q+mp2ZkijlWOtGSJs2zt/afB57CulP4QEkYZ/UUJydusnCwyNbjpZY9EoPFWneXi1aiOHIOGBJ5EwpsG98K0RfzZ9ywiOt1K5PZyjyBPGwaPYPNdg","username_request":"guest","users_request_id":"1","language":"vi","client_ip":"127.0.0.1"} 
[2025-08-04 12:05:04] local.INFO: API Core ---> func: ColCashoutRequest_getList -----> Response:  {"errorCode":"success","errorDescription":"Thành công","data":"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","remote_ip":"127.0.0.1","language":"vi","function":"ColCashoutRequest_getList","checksum":"4c519e0c93917a78a319bf4495c34d5b"} 
[2025-08-04 12:05:04] local.INFO: DescryptData [{"id":888,"type":6,"account_disbursement":"BIDV","contract_id":1072,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NLTC133444564","partner_transaction_id":null,"merchant_id":748,"profile_id":****************,"borrower_id":767,"amount_request":120000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"33f0c2b90fed932f899445b4749a45ad","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":887,"type":6,"account_disbursement":"BIDV","contract_id":1072,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NLTC1334464","partner_transaction_id":null,"merchant_id":748,"profile_id":****************,"borrower_id":767,"amount_request":120000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"eb07db48959262c3dfe2aac2e1bfd5bd","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":897,"type":6,"account_disbursement":"BIDV","contract_id":1072,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NLTC13367664","partner_transaction_id":null,"merchant_id":748,"profile_id":****************,"borrower_id":767,"amount_request":220000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"b3692c4da73faed8dbe9c222b5044c46","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":892,"type":6,"account_disbursement":"BIDV","contract_id":1072,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NLTC17655657764","partner_transaction_id":null,"merchant_id":748,"profile_id":****************,"borrower_id":767,"amount_request":********,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"154c0d1aba3ce3beecc3ae5aff1a8c1b","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":889,"type":6,"account_disbursement":"BIDV","contract_id":1072,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NLTC1767664","partner_transaction_id":null,"merchant_id":748,"profile_id":****************,"borrower_id":767,"amount_request":120000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"5cf365f937ac80762220b163dec9ed23","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":890,"type":6,"account_disbursement":"BIDV","contract_id":1072,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NLTC176767664","partner_transaction_id":null,"merchant_id":748,"profile_id":****************,"borrower_id":767,"amount_request":********,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"39ed9d6fe6752b3a66e11fcbab41b0b6","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":884,"type":6,"account_disbursement":"BIDV","contract_id":1088,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP00000133","partner_transaction_id":null,"merchant_id":757,"profile_id":****************,"borrower_id":776,"amount_request":1000000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"d9e7662d25370f0b46fade9e2dd7a7f7","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":882,"type":6,"account_disbursement":"BIDV","contract_id":1075,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP00000145","partner_transaction_id":null,"merchant_id":741,"profile_id":****************,"borrower_id":760,"amount_request":10000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"e499bce2390e93019b1b8d89e5495a66","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":883,"type":6,"account_disbursement":"BIDV","contract_id":1075,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP00000147","partner_transaction_id":null,"merchant_id":741,"profile_id":****************,"borrower_id":760,"amount_request":11000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"234c46ebd49a3e7088ba46f894ccf976","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":902,"type":6,"account_disbursement":"BIDV","contract_id":1088,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP00000154","partner_transaction_id":null,"merchant_id":757,"profile_id":****************,"borrower_id":776,"amount_request":1000000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"f22c51f3c6c8b7814d76f1c8e20d1cc7","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":900,"type":6,"account_disbursement":"BIDV","contract_id":1075,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP000002","partner_transaction_id":null,"merchant_id":741,"profile_id":****************,"borrower_id":760,"amount_request":1000000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"ce2bd197d0635ab0522302eae9ac0df0","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":901,"type":6,"account_disbursement":"BIDV","contract_id":1072,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP000003","partner_transaction_id":null,"merchant_id":748,"profile_id":****************,"borrower_id":767,"amount_request":1000000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"cce5781296f2e7aff0d33f44202426e9","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":876,"type":6,"account_disbursement":"BIDV","contract_id":1075,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP000004","partner_transaction_id":null,"merchant_id":741,"profile_id":****************,"borrower_id":760,"amount_request":2000000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"ec5cf8f4be0c7411038f19fa16e21c70","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":885,"type":6,"account_disbursement":"BIDV","contract_id":1090,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP00000445","partner_transaction_id":null,"merchant_id":761,"profile_id":****************,"borrower_id":780,"amount_request":989200,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"6f59f38b74d9677cd4a287497ee1a81d","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":886,"type":6,"account_disbursement":"BIDV","contract_id":1090,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP00000447","partner_transaction_id":null,"merchant_id":761,"profile_id":****************,"borrower_id":780,"amount_request":1000000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"23c5afa5dc1930058e11167167b8a838","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":896,"type":6,"account_disbursement":"BIDV","contract_id":1088,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP0000045566","partner_transaction_id":null,"merchant_id":757,"profile_id":****************,"borrower_id":776,"amount_request":1000000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"e1a04c3f6520eccbf2982264bca07230","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":878,"type":6,"account_disbursement":"BIDV","contract_id":1072,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP000006","partner_transaction_id":null,"merchant_id":748,"profile_id":****************,"borrower_id":767,"amount_request":10000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"2df141a5349ce6cedc0802e4d13f70ca","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":879,"type":6,"account_disbursement":"BIDV","contract_id":1075,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP000007","partner_transaction_id":null,"merchant_id":741,"profile_id":****************,"borrower_id":760,"amount_request":90000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"e05d0c7166d5cf19b3ee0b10ef70e4b7","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":891,"type":6,"account_disbursement":"BIDV","contract_id":1072,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP00033346","partner_transaction_id":null,"merchant_id":748,"profile_id":****************,"borrower_id":767,"amount_request":1000000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"dbd869acee70b42d4b91e69febab7355","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":880,"type":6,"account_disbursement":"BIDV","contract_id":1072,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP010010101","partner_transaction_id":null,"merchant_id":748,"profile_id":****************,"borrower_id":767,"amount_request":********,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"edd212972ee7dd6bddaa4a9c55492489","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":881,"type":6,"account_disbursement":"BIDV","contract_id":1072,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP0100101012","partner_transaction_id":null,"merchant_id":748,"profile_id":****************,"borrower_id":767,"amount_request":4300000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"7e216e4401ef276167b087b3b1213092","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":903,"type":6,"account_disbursement":"BIDV","contract_id":1072,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"***************","partner_transaction_id":null,"merchant_id":748,"profile_id":****************,"borrower_id":767,"amount_request":********,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"3c1a09551d663a3cd02c8c8f2a87420f","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":898,"type":6,"account_disbursement":"BIDV","contract_id":1072,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP288877773","partner_transaction_id":null,"merchant_id":748,"profile_id":****************,"borrower_id":767,"amount_request":1000000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"ed27041c8e031c622844f46ccb563574","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null},{"id":899,"type":6,"account_disbursement":"BIDV","contract_id":1072,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP288877776633","partner_transaction_id":null,"merchant_id":748,"profile_id":****************,"borrower_id":767,"amount_request":1000000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"97c9936447b32bf811938dc94017c348","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null}] 
[2025-08-04 12:05:04] local.INFO: sendDebtResponse: {"Fnc":"sendDebt","Version":"1.0","ChannelCode":"THUHO","RespCode":"00","data":"{\"errorCode\":1000,\"data\":\"{\\\"data\\\":{\\\"lendingRequestId\\\":\\\"THUHO-NLMPOS250804303\\\",\\\"mposDebtId\\\":*********},\\\"status\\\":true,\\\"error\\\":{\\\"code\\\":1000,\\\"message\\\":\\\"DO_SERVICE_SUCCESS\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"1071aa609d2b0d38acb109aa186ebded","Description":"success"}  
[2025-08-04 12:05:04] local.INFO: ResultGuiTrichMpos {"errorCode":"00","data":{"data":{"lendingRequestId":"THUHO-NLMPOS250804303","mposDebtId":*********},"status":true,"error":{"code":1000,"message":"DO_SERVICE_SUCCESS","messageEn":""}}} 
[2025-08-05 10:31:53] local.INFO: Portal Request:  [{"contract_code":"BIDV-*************-L1","partner_code":"BIDV","partner_contract_code":"BIDV67482357","contract_amount":********,"type":2,"payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"*********","other_data":[]}],"merchant":{"merchant_id":"*********","merchant_name":"Ten Merchant","representation_name":"Ten Merchant","email":"<EMAIL>","mobile":"***********","address":"trụ sở 123","passport":"58977"},"amount":1000000,"partner_request_id":null,"created_by":"quantri","requested_by":"quantri","requested_at":"2025-08-01 00:00:00","expired_at":null,"is_approved":true,"profile_id":1,"is_required_notify":true,"partner_request_bank_code":"BIDV","partner_request_holdername":"NGUYEN VAN AN","partner_request_account":"*********"}] 
[2025-08-05 10:31:53] local.INFO: API Core ---> func: ThuhoBidvSupport_getContractsReadyForCollection ---> RawInput:  {"contract_code":["BIDV-*************-L1"]} 
[2025-08-05 10:31:53] local.INFO: API Core ---> func: ThuhoBidvSupport_getContractsReadyForCollection ---> Request:  {"func":"ThuhoBidvSupport_getContractsReadyForCollection","checksum":"f5ba22ebd2d699f026f53d472b086b8b","params":"ZVmMchPHeXiOEo/ITADMaDWdjoyWXOvSWa5fR0nD/otqMOEyT4t+NJsaPpQv/mH+M/e8jv/5qvFWrktxN+PQXd1QZ80ay6VyGn9PnBGOvo/nR96aNbuwA5Q5z5k5bzvg","username_request":"guest","users_request_id":"1","language":"vi","client_ip":"127.0.0.1"} 
[2025-08-05 10:31:53] local.INFO: API Core ---> func: ThuhoBidvSupport_getContractsReadyForCollection -----> Response:  {"errorCode":"success","errorDescription":"Thành công","data":"vuk2KIwdPJvsYKq9kLvQnW/4m3HhaWyd4C2bKQhC7Y4sy6K4nsOwaYA6P09Qx9T00meLJvKiEH1gnOd9rWF7S8Y5zHFaYAll7bkg0Tf+B7FcM7VNSgpm5K55RUVJmUGVgLJpJ2DcUoo8WE1QfqXSfbI+/r3Bx3HPL/aThDnpSwsI1wPPypI0h9ED6VdVxPcdzrtmp+a9uZn+OiKXN6k9m9bB2wOW62EM3YFiRnCrtTLNlhuWLE+7LGNOi7lpePjk37jfYzJF6RWxLz+caktC2pNVcjGOG8txt6NBQgoXpDgqnwHcDPZI+cOpcRWrjxHMsMvLQ2hEzo+ai8icYzG8h8XnthW5R15PNyx/w/kPOrt5VmaHSN7TDpY7rOcKEesTbe7yHCdnqtJnxDAqGhilLjlr+ofPxP81giWvWpULwrsZhD6K7r4JCQsYAJQbDTP5+JBwBsWxyxPQzXPLj82LnKfSAcsTx+IY7rsPDxdYIN95jzW/vnpRIhzin6msoX58cEgbw3X79sq6peMtwvWwLUgKUlQopiIZnQcLdJ9WIwLQJhzPPGm9Yn3q44JgUYjcxIfzEyPv12YfFDuolHdjbkQ/DWR8c3qbbYcuOKa86kMj9lOeDldGRMdAyDN1I4xjdapGIx1kokdW+mV8M3RZAM7puukZ6HQCk6qH/IuO7nw=","remote_ip":"127.0.0.1","language":"vi","function":"ThuhoBidvSupport_getContractsReadyForCollection","checksum":"f903b56a3080a4f52f8f2aaccf8791f2"} 
[2025-08-05 10:31:53] local.INFO: DescryptData [{"contract_code":"BIDV-*************-L1","partner_code":"BIDV","partner_contract_code":"BIDV67482357","contract_amount":********,"type":2,"payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"*********","other_data":[]}],"merchant":{"merchant_id":"*********","merchant_name":"Ten Merchant","representation_name":"Ten Merchant","email":"<EMAIL>","mobile":"***********","address":"trụ sở 123","passport":"58977"}}] 
[2025-08-05 10:32:41] local.INFO: Portal Request:  [{"contract_code":"BIDV-*************-L1","partner_code":"BIDV","partner_contract_code":"BIDV67482357","contract_amount":********,"type":2,"payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"*********","other_data":[]}],"merchant":{"merchant_id":"*********","merchant_name":"Ten Merchant","representation_name":"Ten Merchant","email":"<EMAIL>","mobile":"***********","address":"trụ sở 123","passport":"58977"},"amount":1000000,"partner_request_id":null,"created_by":"quantri","requested_by":"quantri","requested_at":"2025-08-01 00:00:00","expired_at":null,"is_approved":true,"profile_id":1,"is_required_notify":true,"partner_request_bank_code":"BIDV","partner_request_holdername":"NGUYEN VAN AN","partner_request_account":"*********"}] 
[2025-08-05 10:32:41] local.INFO: API Core ---> func: ThuhoBidvSupport_getContractsReadyForCollection ---> RawInput:  {"contract_code":["BIDV-*************-L1"]} 
[2025-08-05 10:32:41] local.INFO: API Core ---> func: ThuhoBidvSupport_getContractsReadyForCollection ---> Request:  {"func":"ThuhoBidvSupport_getContractsReadyForCollection","checksum":"d2a9c38bf7dae72ef9c843fbb75f9a28","params":"ZTIFcteVZch1AA6o8D26W/yDooqOoNNlnlR+QGRV6D921TN52sZkQO/Xt/X+e9lpGVSaFAhEQNgH3UnqxfuhY4gh0UtxdwsxI+V2tf7hqTu+/1B1f52Rp4geTpC4FN1F","username_request":"guest","users_request_id":"1","language":"vi","client_ip":"127.0.0.1"} 
[2025-08-05 10:32:41] local.INFO: API Core ---> func: ThuhoBidvSupport_getContractsReadyForCollection -----> Response:  {"errorCode":"success","errorDescription":"Thành công","data":"v9K5OSw/uWjDrvFtC0m5LKmuo0IvSy6eN0G9pILVdP0xz4cPQZ+1jl/y2izC5EllCR+JCWlUslnN5z4TlsMyn818BlaTUA8uxp/44LEH6h511KvcoeZtySZADxCMDxZryAfkLS0izDRuRW6kqKXlPVJO/1ZL9P2Yz7k856sDgRjs6ANbmWmR7WdbtSZAIn3hIRlaV/rxUW8pXpAMQksIrZDH1xCT6dY3OFi4DoSUjaO3PC8m6Vm8A8UOjq+8j1s3snSWJJE8ecF2j707aZ7k7sfyw1kU2GkTh7hZMZyUBKBi6arR7mKt54tuzub1e9PQJjBAH6QmG9tfprpy0DslM4DvD9PuxffsxXN4YyotC6hPxnubp2g2Ep/8QNZ33YBQF4qKBG2ZeTNwQ3ym9NMuiX5RClGAxm0a+IxRyCzjlH7zdLstj6h+ik9LfWOlEfNVy1S5BZtqPkrJjXywadqh5iqnZd50f4eaqdrV/lvHg0MZaeqq0BLoJWfv6lWD6QzQ2/dS6Dd3sbXA9pRGLG7gEcClewltIsGiUPGH6LJ/zyT/KwpBsMj9/C92e+Ilx6tqFPlyaYn9kHymwUaLeWGpu5ZxGpgzojUVtn4KJjyKcHPMBNPPTRwAB0hb5bYhHjBfEZ/Xb5b14y6c/sSI6+Iv7cgduQ9dOzsGtLKi4guWUhM=","remote_ip":"127.0.0.1","language":"vi","function":"ThuhoBidvSupport_getContractsReadyForCollection","checksum":"df4f0dbb2b52f7fa8d7cfab5efb2e3f0"} 
[2025-08-05 10:32:41] local.INFO: DescryptData [{"contract_code":"BIDV-*************-L1","partner_code":"BIDV","partner_contract_code":"BIDV67482357","contract_amount":********,"type":2,"payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"*********","other_data":[]}],"merchant":{"merchant_id":"*********","merchant_name":"Ten Merchant","representation_name":"Ten Merchant","email":"<EMAIL>","mobile":"***********","address":"trụ sở 123","passport":"58977"}}] 
[2025-08-05 10:33:35] local.INFO: Portal Request:  [{"contract_code":"BIDV-*************-L1","partner_code":"BIDV","partner_contract_code":"BIDV67482357","contract_amount":********,"type":2,"payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"*********","other_data":[]}],"merchant":{"merchant_id":"*********","merchant_name":"Ten Merchant","representation_name":"Ten Merchant","email":"<EMAIL>","mobile":"***********","address":"trụ sở 123","passport":"58977"},"amount":1000000,"partner_request_id":null,"created_by":"quantri","requested_by":"quantri","requested_at":"2025-08-01 00:00:00","expired_at":null,"is_approved":true,"profile_id":1,"is_required_notify":true,"partner_request_bank_code":"BIDV","partner_request_holdername":"NGUYEN VAN AN","partner_request_account":"*********"}] 
[2025-08-05 10:33:35] local.INFO: API Core ---> func: ThuhoBidvSupport_getContractsReadyForCollection ---> RawInput:  {"contract_code":["BIDV-*************-L1"]} 
[2025-08-05 10:33:35] local.INFO: API Core ---> func: ThuhoBidvSupport_getContractsReadyForCollection ---> Request:  {"func":"ThuhoBidvSupport_getContractsReadyForCollection","checksum":"1ecf1929cae4a2fe553268320995b79c","params":"hxdifRFPloorlMNUXhA6lRfNG7salDlwxKAw+o95BfQXmX8OyBs3gbO/3Kx/RdeykrC1OkM7FSYDfVnyd/tI76P55r690i58T37bJ2mLARkPdbhzY6i35dLcvNqG/S+Z","username_request":"guest","users_request_id":"1","language":"vi","client_ip":"127.0.0.1"} 
[2025-08-05 10:33:35] local.INFO: API Core ---> func: ThuhoBidvSupport_getContractsReadyForCollection -----> Response:  {"errorCode":"success","errorDescription":"Thành công","data":"lGrSiHZugNxpHj8DWjRkOCBtlzkGuSpNRElAOx/tgY0s7BBG1pVb/HxT6pXft7eEcrj16WLTGw3CnfUJWRyh9ktg7T4Z6Yn4awDzynLrzLh8Mfm0ETfEGgqZaeTXM1zbG/acg8USqMQ2wgmyueSKl8AoN6Tp9ZHqzzzNERF8bAs6ycdIA2WKMNVuCH8BhfLyMCZjDL74nxMVBKqAHFW3qR6AC03HH9kI/ZTkVgl2s5Quanx/hEnZ5TiVjWPYvSadQ9ukVZio4rSAd2z9QhbR+CY6TEkr5PudHqNVcXSIHNZDIxFdLUTW2H2tKcVUWLrgyL6jpyRm+m0v+EoyAkvF7dMq2IWxnjEoeHe2dtEqOXkdypGtUmcWq1/dELxltAAPwp624QuAs3opiqWZWC1njEPr4ZquLPBINToq03jhGS6bMSyQmcjme8Jjem2uoYXyc/XRP2xVCG6lqbHNF7yajxVO/qLZ/HvXGTQw7rFTUnn+PkE5dBYmfyCeRQ76b70I6Z90yXUbwxpx+QsqR3+f5LYSUY0KBKeJOQh1+zZXYCpqtrzW2gF9NwRrgbNy/orXjtwnM/gXo/Y/heHkqIZipyjvwBCvKkH2c5NNeyxVzuIvhTiCDhsbYYkhxP0c55uXjAycGOZcOYpjD+ZKgkCGV5BLRruVOn99rmc6o9Ld3OY=","remote_ip":"127.0.0.1","language":"vi","function":"ThuhoBidvSupport_getContractsReadyForCollection","checksum":"6d53af8faf956c8e0c2c200c57e777a3"} 
[2025-08-05 10:33:35] local.INFO: DescryptData [{"contract_code":"BIDV-*************-L1","partner_code":"BIDV","partner_contract_code":"BIDV67482357","contract_amount":********,"type":2,"payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"*********","other_data":[]}],"merchant":{"merchant_id":"*********","merchant_name":"Ten Merchant","representation_name":"Ten Merchant","email":"<EMAIL>","mobile":"***********","address":"trụ sở 123","passport":"58977"}}] 
[2025-08-05 10:33:46] local.INFO: Portal Request:  [{"contract_code":"BIDV-*************-L1","partner_code":"BIDV","partner_contract_code":"BIDV67482357","contract_amount":********,"type":2,"payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"*********","other_data":[]}],"merchant":{"merchant_id":"*********","merchant_name":"Ten Merchant","representation_name":"Ten Merchant","email":"<EMAIL>","mobile":"***********","address":"trụ sở 123","passport":"58977"},"amount":1000000,"partner_request_id":null,"created_by":"quantri","requested_by":"quantri","requested_at":"2025-08-01 00:00:00","expired_at":null,"is_approved":true,"profile_id":1,"is_required_notify":true,"partner_request_bank_code":"BIDV","partner_request_holdername":"NGUYEN VAN AN","partner_request_account":"*********"}] 
[2025-08-05 10:33:46] local.INFO: API Core ---> func: ThuhoBidvSupport_getContractsReadyForCollection ---> RawInput:  {"contract_code":["BIDV-*************-L1"]} 
[2025-08-05 10:33:46] local.INFO: API Core ---> func: ThuhoBidvSupport_getContractsReadyForCollection ---> Request:  {"func":"ThuhoBidvSupport_getContractsReadyForCollection","checksum":"bb2363d3a881f9498127c474a3997a44","params":"UzlpjQfA0Rcs/oa/MrT6xURxCTERDwfUFBYEJ5hv45IfZsj80bS6xyKuN7OAOU1H2zk7pPK5i6kWtgvjCfEgRYbqUiHaFb2gVYfKsNu+MjkaM1Zd78FpAAEKRqNsfjPj","username_request":"guest","users_request_id":"1","language":"vi","client_ip":"127.0.0.1"} 
[2025-08-05 10:33:46] local.INFO: API Core ---> func: ThuhoBidvSupport_getContractsReadyForCollection -----> Response:  {"errorCode":"success","errorDescription":"Thành công","data":"enQ30YCTlN9d5zOLnTzslO125kSU1bAY1PPLnkbU+HBivltF9N3QJcg+fJLcWpjZXFS8RuFKM8rJqcFaSVHcE/ILrVIvd+rMk2ZYhVLsHQVsaUMXjuQgf9onZibo8+7xuNgZPiBOP/LZlGcxNvkeiaG7YFn4qYbHuLFwSsFgxfMhNF6q+UMUBr1E6GfaZw0NW1iMuXx1hRDTDzqWaZ22dF/Six2p+eR4NZ0m7aMfuS7N8RMizR1FpRSFEh6vPWoTHGRqY0v9twg8CXcCsmjuBjNA2TNZnO1KGSk46K9kRBgri8tCEOUFRaH6kwkS9YFjc2J9/pdpnZCvNtMMRF/eZlVO5t+5MsB1ItMwz+aN0E+6XIBd2A0ESBOrR3MaaV67rpw+G+RPOaOJ5bYz1Y8UUshUOsgvTkGmhaqwTX3YSlYI4VHT2TTiU4glc3jpNewjKqu65EdfZJv69xvwNhUJQd6pNvajK8+pp6wMWhzcppzcKssAvKdm9NL1pZAhVKSTgFAm1FsLWanR4AZiIbkJoSrN7hCnG4wLFHTZzS4BnUQ8UNy95mNgr90GLKC9UN82+uRZKvn9yznL6xxIFqmbPjurqKPJFpzNgw2UqjVFpC2epUN5Unu4x59DKK730p5A2kqb7M8FT8APPmBzarb9kLDrxHzEQ52BcJSLSNnrBMo=","remote_ip":"127.0.0.1","language":"vi","function":"ThuhoBidvSupport_getContractsReadyForCollection","checksum":"b76a31f6cf3bf7d8ba922af726db9df1"} 
[2025-08-05 10:33:46] local.INFO: DescryptData [{"contract_code":"BIDV-*************-L1","partner_code":"BIDV","partner_contract_code":"BIDV67482357","contract_amount":********,"type":2,"payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"*********","other_data":[]}],"merchant":{"merchant_id":"*********","merchant_name":"Ten Merchant","representation_name":"Ten Merchant","email":"<EMAIL>","mobile":"***********","address":"trụ sở 123","passport":"58977"}}] 
[2025-08-05 10:36:46] local.INFO: Portal Request:  [{"contract_code":"BIDV-*************-L1","partner_code":"BIDV","partner_contract_code":"BIDV67482357","contract_amount":********,"type":2,"payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"*********","other_data":[]}],"merchant":{"merchant_id":"*********","merchant_name":"Ten Merchant","representation_name":"Ten Merchant","email":"<EMAIL>","mobile":"***********","address":"trụ sở 123","passport":"58977"},"amount":1000000,"partner_request_id":null,"created_by":"quantri","requested_by":"quantri","requested_at":"2025-08-01 00:00:00","expired_at":null,"is_approved":true,"profile_id":1,"is_required_notify":true,"partner_request_bank_code":"BIDV","partner_request_holdername":"NGUYEN VAN AN","partner_request_account":"*********"}] 
[2025-08-05 10:36:47] local.INFO: API Core ---> func: ThuhoBidvSupport_getContractsReadyForCollection ---> RawInput:  {"contract_code":["BIDV-*************-L1"]} 
[2025-08-05 10:36:47] local.INFO: API Core ---> func: ThuhoBidvSupport_getContractsReadyForCollection ---> Request:  {"func":"ThuhoBidvSupport_getContractsReadyForCollection","checksum":"6c6ef11f8323fcb6ed0f5c3b13ffe3aa","params":"JLALQHXgokQH3dkSRinnqJiL0LB8zdPLJ7Zi4YfrJtLMdBCtq4TW10G/KOZFk75hH6dLqbqN47mZ3ypOdzSJ8vF5sv0WZIc5j4/iRaJ5I/0LFgko2bCBx+j+LWpf04eg","username_request":"guest","users_request_id":"1","language":"vi","client_ip":"127.0.0.1"} 
[2025-08-05 10:36:47] local.INFO: API Core ---> func: ThuhoBidvSupport_getContractsReadyForCollection -----> Response:  {"errorCode":"success","errorDescription":"Thành công","data":"3pIKfLgP6/xh0+KBRaNJeB7h1S2YjHiafshQyXgmioz7x4RzSLSdqB9YPfFvaEsyFZgG82xS3O8cl6VwGpReyGapwk5z2LnpYuNOPFdiiYIBl602ohlEXaI0jTFuwU/tUZOd2FeXgzZtQY6tBIqHjRTARIDu6mEUVyRDjqYBk//Ms3/zdmuzG35voTFyoMn0pepoi4DDVczRMA2XaHIlxG2WGO9hULMvmXCs/Hhl1EWaVXK7VPvkSPdh1vspDjLuHjaSZk7+prk/g+DVFgckQBsByF/618uYDvsKFLw7Rtoe2gn84BCgmbbRk5EtYlqDEC+sIKAeXsmVi2EBxGwGXvGKr5FlQn883BJZTn5twsczj4fP6z4i8w3QHpueID1KmlJ48v645t0Wjk+6VD+GAbF/bkJu3meisvzvQ/ckPHulU1wSJFGkYqn/PFCG6/u9IcQl33/gVN4mcj5c7I+YEy27wBYdRCyzD5hSG0G/VpD+QKtfqfvCuUqwEUsHvaNIUyEXr1MajQFPTG9HknUj67Ou//tE+U3xJ5dJcUcB7MWml68iIFdFnsj2ojp2JEMym8/R0thRTv43CkM63m1w47OiFf/zDkJXK67HZT2Z/Ovh/daIQaoNWKEkXqCgeipkjyLzuZUUg3SfnbEj/j2R5xQQ/DJpgI9lv5+MD20aQBI=","remote_ip":"127.0.0.1","language":"vi","function":"ThuhoBidvSupport_getContractsReadyForCollection","checksum":"d94cf33c366c4fecd2cbfc0b33f1623f"} 
[2025-08-05 10:36:47] local.INFO: DescryptData [{"contract_code":"BIDV-*************-L1","partner_code":"BIDV","partner_contract_code":"BIDV67482357","contract_amount":********,"type":2,"payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"*********","other_data":[]}],"merchant":{"merchant_id":"*********","merchant_name":"Ten Merchant","representation_name":"Ten Merchant","email":"<EMAIL>","mobile":"***********","address":"trụ sở 123","passport":"58977"}}] 
[2025-08-05 13:35:48] local.INFO: API Core ---> func: ColCashoutRequest_getList ---> RawInput:  {"partner_request_ids":["NP0000045566"],"cashout_request_ids":[]} 
[2025-08-05 13:35:48] local.INFO: API Core ---> func: ColCashoutRequest_getList ---> Request:  {"func":"ColCashoutRequest_getList","checksum":"abaf56ff472e8a967d42d3e306c20aec","params":"9Y+myjsuJVg8mNJxQa6BPnOHxetKsfRZQvUeTVqP9kESFI4FKkEUDQ7122luZk5JqzQz/c3bn+k546UMonw9bEwIA7zRCp8rJcKVCkSHc8uMLzLYZQU72YYFGcK4f71i5wWCz9y6KiF68oKPMeTtWw==","username_request":"guest","users_request_id":"1","language":"vi","client_ip":"127.0.0.1"} 
[2025-08-05 13:35:48] local.INFO: API Core ---> func: ColCashoutRequest_getList -----> Response:  {"errorCode":"success","errorDescription":"Thành công","data":"6xbrp28NXGtBJN0GvMq35rJKwvdVLChjmsyrKTkXO/c+BhCkQzt3qT2FADTX1B8kpOYFn71ba4lBu5lwtf49YOWkl5sUZzx+p1ZGiZ7UwNmo5UiVO8CbpuoPbEh/ewYtPu+uTJHLRke0zsbq29pMqgm3V8xvDbY6f35JBIPoKvc6XWXI0XnYbLwumm7r4IioQHXXZcWMaxc3HmBEvtIkCSimwd4G2Vbk5bTDxYqrIhoCDA5Xs1sohDyDvVZx/QiHX0dcO9l4FIZ/k8hoYCUtKqljTBKsZKViJLwC1GXK2/Mztcy6EoI8jSu9V74KZai84X8v4OhbwbN3O3ebZfxyOK2i34L8u348PejSQXixvsn9yljoYdZU3ubeAPz5evs5vUCASQI8oyUyF+UsjC/1A1pcfbp17zLI7knzy74DGk3gA+I3MHWmG9Fr+35L7WLEId4fDwaOWSEmLpxy2gIIT8h9Bn4uDlOGmza3XUq4qjPAFAQ7XCk+MTO+GpcDml6ocU2ngqjOkenHsz3hDWxp8iBYcvbz/K6H4f84rRIVnnOVC5vKOYbTOHA1g9z1PH1FaU/7WOul8s85C+4ewOJ0/Oy4yRvb18pPExwGK7rTc+awb+rMCpVguj4MP2dDJsZuFygCZcKFtKTzw17YY07c1d0VO3lstRV+gyEnRAOkIKKhfHRNBiTs9jYB4kxiKGhtXKJEy/wLm2smhVO+zsAXJJlCWIv8a8fpTw4NLEsNTW8kRJfvQIcYIvHvtmMwKq5Ii94wWAVS1X6Nn6ZLmdWNsu+PJYfgvINYF4QhKu43Sjxmpe5UaQquBvvcRenHM6w1fb4fqkLecyCFxCfjl31Qc0en5z+PaxNsIhzGbnVaOMBFW33pq88w3OmBbWe4y5POP+zJJYwHAQk6YnXJ7bQy5zBMUF7Gn4zamUcpJWDwXj9PF7S1iFN58qfhJkzNnCF49P+1sFTJ7NLMsq98oK/K4LU5i9BZRVWdGjUjODfAu7wCvfIHrLvlSiZIfMSKuxAEWz7l+Ng4b7ytNpVL8CpnVSHroSJQJZRJqLy4GhSDdWcIKf3HKB7VjCQGwT1veR9a","remote_ip":"127.0.0.1","language":"vi","function":"ColCashoutRequest_getList","checksum":"60ec18c1646d15328819dbadd64cb0ce"} 
[2025-08-05 13:35:48] local.INFO: DescryptData [{"id":896,"type":6,"account_disbursement":"BIDV","contract_id":1088,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP0000045566","partner_transaction_id":null,"merchant_id":757,"profile_id":****************,"borrower_id":776,"amount_request":1000000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"e1a04c3f6520eccbf2982264bca07230","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null}] 
[2025-08-05 13:39:49] local.INFO: API Core ---> func: ColCashoutRequest_getList ---> RawInput:  {"partner_request_ids":["NP0000045566"],"cashout_request_ids":[]} 
[2025-08-05 13:39:49] local.INFO: API Core ---> func: ColCashoutRequest_getList ---> Request:  {"func":"ColCashoutRequest_getList","checksum":"d911c2640f533acd806e43a4c6311340","params":"VpBm4HRr3+cMIl0Qv3tTW7C2q6yYhxSiMUB2mOes1w5eurYjXQWBXek+WryaGPUl7RmB2TH6OSN28xlYiV4fM3OQowniBUYkHQ38rhRyTmxtTFH7pHUYFVT4JN2sdjivwbeqq28PTsKs0iSieWdvBQ==","username_request":"guest","users_request_id":"1","language":"vi","client_ip":"127.0.0.1"} 
[2025-08-05 13:39:49] local.INFO: API Core ---> func: ColCashoutRequest_getList -----> Response:  {"errorCode":"success","errorDescription":"Thành công","data":"/RdkzIWbk+Ya33OlxpdwXHVVHYVtC06YphHeHxS5+ic52ha9pFNpRlN2CPXiVc0ddYZ4CPzeEsIIiV+/RGJGxrIMZVmyeM4he7RwJHHuNdT6Lhh5oEyrR4BkSXM8x9YlzLPV1btXj5A8J7164i3VvWrNK0R/aGzka5LR0NirQJBmkwFrvjbctH+obM44uTyyNiTPAgIxBtqbffXH3QcRoRBk4xJN21MWf1cVnAsShEnRS8TaEM99pbn2ESHZhCoFF8gBgQuuFA1ZYasVagoxCiNiOIe021KusBNqtBzQ+UdhfAQPtUbP4Y2A8jdRknufIyGQlA1iX4Xt2+F6rCuVBN+XoVBr710+laNeURUdL6T2FeNe7jYr45dxvBVJOjFwo0nQG56cmkjH0DYxM9HHsIU3xVucpbSJ0jfOF9k10aw1tyz4/rCisbBf3sLvnkmkp40Nr4+z5i9XgVO29lk4elHBeHzErQ1zJ2SXi+k2XB2asRkpgThU6/CGZE+qeImI0+bioqOeEafKWzY9XDlu1M18CrzrSiVQsDbqKfAA625VZX1O5hIn+cRh3MRBf1Mjiqk1s4kCWDbeZixT34qW2+NlJMsIqQqzKrS3ea/7KmoFSmGvqlfe3PWo5cm1jYldHSL2IGGaKE4wx676aLHOfnL8BWp3gxXtQZ/UFAGY1DHzyxMCYGjLzWpo9kj01xmQ13h2vcyqW+QA+5j6098yQHC0FT8KIwAonzrQ594pxkZG6jxdWNrFsNMNCK9lfeucakVLcyk6pAbRZNK1Dgcvs0eF6AAAl+e/f4YtpPkgSjg9c0fzNDY8Oto9fTKDRPvMJi9qw0doTkMVxqBrm1ZUhN+kRxf2ImoxjWfMc+xHJOLPR6g929JXJ8EEZI2iOtGzxOW8Tlsz8ycXN/DkxbWFZelISfspMmgBol/4bYsJBn91Rdd0UW1Ky3TDgnn0/r5eA7WW+5RYnCIw9EE9L+k4r6GoqXXrT1fLU4CrAfJ1Us+JEPv8XwoKGTkSgZw0vPg1k51S9QVxH/iQFrBMig6KQHfnjiu0O9Mm3wMGzDgg3rsNwyeF6e+1oKUWfOb77q2T","remote_ip":"127.0.0.1","language":"vi","function":"ColCashoutRequest_getList","checksum":"81e2a8c4d4408bb5a7d475ef36099e5b"} 
[2025-08-05 13:39:49] local.INFO: DescryptData [{"id":896,"type":6,"account_disbursement":"BIDV","contract_id":1088,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP0000045566","partner_transaction_id":null,"merchant_id":757,"profile_id":****************,"borrower_id":776,"amount_request":1000000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"e1a04c3f6520eccbf2982264bca07230","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null}] 
[2025-08-05 13:40:00] local.INFO: API Core ---> func: ColCashoutRequest_getList ---> RawInput:  {"partner_request_ids":["NP0000045566"],"cashout_request_ids":[]} 
[2025-08-05 13:40:00] local.INFO: API Core ---> func: ColCashoutRequest_getList ---> Request:  {"func":"ColCashoutRequest_getList","checksum":"fa15f904aef0a7c7ff63e5ab034f7522","params":"rj+2InxV/A5WESpAEOu4FTSX7FPL/zxuNAY3Ueh/1QUr1/UIj3jxmKIf9iuvoMpopKTqHuGZRT17YYd6zg0oXJ/4i722gcFGxtt6urMnfDUYn1TSirpDk0NtT0zk8IPJrqO2xI65JVkGeGPMxb3tVw==","username_request":"guest","users_request_id":"1","language":"vi","client_ip":"127.0.0.1"} 
[2025-08-05 13:40:00] local.INFO: API Core ---> func: ColCashoutRequest_getList -----> Response:  {"errorCode":"success","errorDescription":"Thành công","data":"OtuQmLbsLVkP5GRpjbSGUFcXFujXtPek00Xz/fALmdAU0bhhXAdaRQUowW4iIxSixGZO6zqW8Q7BqKlqvMb+0P+0dm3suRc2ZMjykG7bUq5GzU6ffT9Wz5simg+nc/S1O/3u5EcAdsunjq7f0Nh9uJJfykJIO8B087PW2Rldo3mly60ScnTl1ZHZTN7pCv/KEx+7OUeq4qseg+bglGkiGd+HWp50zT21U8lYkOd+ioaEziaP94k3ZQNzfXqVKGZVR6cbo6SWVUgzuxYSLu0skX8MCNlebAKPDzNBFmxVeCOs2051Fq37YBo/kj8HKkvtVM/pNHFjMIKA4tbK5ZMeMkrM1w5u2ohAk+0y7fRlxfhLsfdfWY8P2xUJAZa5FoEAM4R9EswhXyNq83yhtvGu+WbltRM/L23+fIY0Js1f4xTRs8+fLBKwmpvTEZz2vTUehzUny4CrI6nY6XEDpFfB5Vg97US5f5qz488D5ECckakvJd+2DF8u2+1+2VwKMHePevVSkymwxPvGaZSEnelFFIGuyJaG/ohPfv68R9ssiNzl8+/PLSqMZ47AO8T9f8fpxbH9S7l7TOBb9LX4TX0YIYm3ogelQxFGozAF00x0prk5aItUlsElteNLTL7We3upXf9PjezP3uFNH+HyEK3xU2OhRUP4NJgbizBAoEM+vixmsO3AjOWqTQ751/+eVIicWojEfMj+M1FdoCjkEJC9JaGH0RxJ0N4AMuGLz6JmGnk3HY0jF/Hr2dnluRG6r11vbhUYQtwgPvNAlGZbyGi+zljVHsMDhIGA9YVDUunioEzscvEEvaZHVptPnOaZTyOLJM9/DilvssGHuDV9UaI2nzCky34eC6SeirKaxFuRsJx4ObZn1HrEUlFLX+UeK0ct5zj8ZoYWHWXTfZwQa+SP9Iys4yce4fGYOm1atEGxPLs5iqMOi0gAsRnrsV+ulwqWvwzmL3X4dSmdGMAXkx47aeNgQAO3pLXPuJep6dqzU1vhs4uEbGkYjxD53V2ScRIJTxHRb5BSQ61T26OA+BMjjOwgJCY70iCTssRCZTlhF4P8RP/M5Ykd/azWx7VRK9Aq","remote_ip":"127.0.0.1","language":"vi","function":"ColCashoutRequest_getList","checksum":"074ea22526aa89173a62c2d59ae63902"} 
[2025-08-05 13:40:00] local.INFO: DescryptData [{"id":896,"type":6,"account_disbursement":"BIDV","contract_id":1088,"transaction_id":null,"request_refund_id":null,"overdraft_cashout_request_id":null,"order_code":null,"partner_request_id":"NP0000045566","partner_transaction_id":null,"merchant_id":757,"profile_id":****************,"borrower_id":776,"amount_request":1000000,"amount_settled":0,"amount_approved":0,"fee":0,"amount_fee_evaluation":0,"currency":"VND","cashout_method_id":3,"bank_id":13,"account_holder":"Nguyen Van An","account_number":"*************","account_bank":null,"bank_branch":null,"description":null,"reject_description":null,"status":1,"status_request":1,"checksum":"e1a04c3f6520eccbf2982264bca07230","time_created":**********,"time_approved":null,"time_canceled":null,"time_rejected":null,"time_completed":null}] 
[2025-08-05 13:48:11] local.INFO: Portal Request:  [{"unique_id":"c4514d61-f49e-4233-887b-d9fd45f4a629","contract_code":"BIDV-*************-L1","partner_code":"BIDV","partner_contract_code":"BIDV67482357","contract_amount":********,"type":2,"payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"*********","other_data":[]}],"merchant":{"merchant_id":"*********","merchant_name":"Ten Merchant","representation_name":"Ten Merchant","email":"<EMAIL>","mobile":"***********","address":"trụ sở 123","passport":"58977"},"amount":1000000,"partner_request_id":null,"created_by":"quantri","requested_by":"quantri","requested_at":"2025-08-01 00:00:00","expired_at":null,"is_approved":true,"profile_id":1,"is_required_notify":true,"partner_request_bank_code":"BIDV","partner_request_holdername":"NGUYEN VAN AN","partner_request_account":"*********"}] 
[2025-08-05 13:48:11] local.INFO: API Core ---> func: ThuhoBidvSupport_getContractsReadyForCollection ---> RawInput:  {"contract_code":["BIDV-*************-L1"]} 
[2025-08-05 13:48:11] local.INFO: API Core ---> func: ThuhoBidvSupport_getContractsReadyForCollection ---> Request:  {"func":"ThuhoBidvSupport_getContractsReadyForCollection","checksum":"c9a89363b82eea284fcd73b6f254aacc","params":"vjZywnR0fArt6h5PM6WjbM2h2iP6f4efpdnuJtuwdpRLXK7OmHdeQNzqhRs9F1CjlnEdk1K7r5APooJSC6lNyQW0zVqYxp7Ryxkhm5IcuI5I7Zc8NS11zUz64Y08BDIY","username_request":"guest","users_request_id":"1","language":"vi","client_ip":"127.0.0.1"} 
[2025-08-05 13:48:11] local.INFO: API Core ---> func: ThuhoBidvSupport_getContractsReadyForCollection -----> Response:  {"errorCode":"success","errorDescription":"Thành công","data":"Fz4ZDiRxf7IPdMBYChHCmrXleqKAO8SWKuOXrUMseRANa6BzLy1JMPBVe1WaTH73CCHRVE41+EgoPOMBMDxxjPC/bMChFJ3835XyrlJcRytopVgv9Q4vXTS9hXV9G/fCvCXOBLqMN8j+mzoOZelApmQuwRp3/Mrt8aGgAFpJ9z+zu7FtZlPy6P0JWBm0rH84/Zkjf1thZGOoaDPGLmnsJQGQfiqKBUIapLl/+b2AoZ0WUvwC9XCB9e9AYTQ46a2BvqRmOacN9/yYGL0b0KSTY73ZuxdCkqxOyXO3n8/OEjKpxoXc1Wpzh5uYNvjRIbT53mw/hzjCFX3FYgOO/5v/zvn/AI6NksybFQ7w9i2pyzfwtyp6mUlLSYUj6BtOI8OzW0W02kR3Kgmnh8jmsbjFjBx6NZrXRPAqXo4rMj2QKjh2as6xooDnEAJE0Jhkf0F8QgY1w8pek9eTzA5Qzoybe5DeZNgEm6VYWoAzvC1YN4dIc7hAvG/PD6MRbj7jUgNMiGyIVrDO8ghnY8EkIyFl/YshcSChNKgqqsUk9coPFqqEyNXImUAcJeebqm5kcFhiDReD/thBqCRUQaqX0BQC1CGbqj3KO08gLJzxtu/+kXE8RQ1OigFV2agpOIzKJ0/ZTYm7Bjag3sPKlUHlIYhqmORVR615oml9D04CyX2EyVA=","remote_ip":"127.0.0.1","language":"vi","function":"ThuhoBidvSupport_getContractsReadyForCollection","checksum":"2146a211290b01d0a166bd566832fa2e"} 
[2025-08-05 13:48:11] local.INFO: DescryptData [{"contract_code":"BIDV-*************-L1","partner_code":"BIDV","partner_contract_code":"BIDV67482357","contract_amount":********,"type":2,"payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"*********","other_data":[]}],"merchant":{"merchant_id":"*********","merchant_name":"Ten Merchant","representation_name":"Ten Merchant","email":"<EMAIL>","mobile":"***********","address":"trụ sở 123","passport":"58977"}}] 
[2025-08-05 13:48:17] local.INFO: Portal Request:  [{"unique_id":"c4514d61-f49e-4233-887b-d9fd45f4a629","contract_code":"BIDV-*************-L1","partner_code":"BIDV","partner_contract_code":"BIDV67482357","contract_amount":********,"type":2,"payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"*********","other_data":[]}],"merchant":{"merchant_id":"*********","merchant_name":"Ten Merchant","representation_name":"Ten Merchant","email":"<EMAIL>","mobile":"***********","address":"trụ sở 123","passport":"58977"},"amount":1000000,"partner_request_id":null,"created_by":"quantri","requested_by":"quantri","requested_at":"2025-08-01 00:00:00","expired_at":null,"is_approved":true,"profile_id":1,"is_required_notify":true,"partner_request_bank_code":"BIDV","partner_request_holdername":"NGUYEN VAN AN","partner_request_account":"*********"}] 
[2025-08-05 13:48:18] local.INFO: API Core ---> func: ThuhoBidvSupport_getContractsReadyForCollection ---> RawInput:  {"contract_code":["BIDV-*************-L1"]} 
[2025-08-05 13:48:18] local.INFO: API Core ---> func: ThuhoBidvSupport_getContractsReadyForCollection ---> Request:  {"func":"ThuhoBidvSupport_getContractsReadyForCollection","checksum":"3c8d3062e6be108dde8cf39b824e7a44","params":"Smvfi5kW4Ij3q3M9TeVM2G5/2TLk7tb4GMpoyW/lHFZCwokSG4w6Hy/A10rm0lbRR92z6Mex2RZmAG6EViK2Jts5LhgwV2zJPfmKzg4cv6zAqeTqxuKyCBFQbSKwYQjs","username_request":"guest","users_request_id":"1","language":"vi","client_ip":"127.0.0.1"} 
[2025-08-05 13:48:18] local.INFO: API Core ---> func: ThuhoBidvSupport_getContractsReadyForCollection -----> Response:  {"errorCode":"success","errorDescription":"Thành công","data":"g8F3VhrR0X50WrjMoV+bPguRD+/NkhhM1ZCFxwGyQusaa2p8G8coKBLAjo3jDeTGzallVKE5xudi3fkplD6Ic9xJWmgwRSBdXS/wgtrA4Hwf73VjZ3SdnHuEwsiLbxLU9IfazFhrEYbA0xeunWj2aKLrxKq6g4DTwfk+tfM1mk6avmrkxrvLJodLOboBcfZzuVf/pa5L+jLr7GmgH/auYEk1H/SIxmrIV5j4Zel5p3GcG1BUB5VxUWCRk2UDpatshybhOUQd8TW67onb0MrxmDaPuYfP8muuhu0VJfTkVU6A69EwFIy5H4MhyX1kav7qbBTXGoqfBVd6+aICXsWxJuKf0h+vOPar2XyWb0DMiJNbIf8J8LjJtVAOFe0FztFxPfc4RR1FTBfHljtyLCX1mlqxfDhbatvw6VdD5Eiro1nKbknoe7OoADThodb0QnhHI7Z9D0Ajiz7WC0/V6vO+DRJEGSPcchxIC8vh75TTIwfs/6lyS2OoxlNJpAYSDdIna2v6mq14H3hK/n0MxxAGu1YRtwXGQYwH7SGpmeUneIz17FLUgpyaJNeNA54Kl4MlnoWfC7WYAB9VkW1dvWKrWrTIyIp/R0PwDn/fEdUU9moGNzGm390jhZRjuj23g9R9Qm4EqvJm1wMwf8Et9Q53N4d8y5SIqxH/t2YE7JSZiAg=","remote_ip":"127.0.0.1","language":"vi","function":"ThuhoBidvSupport_getContractsReadyForCollection","checksum":"4846a76d6640c87ecfc5ba696ae0453f"} 
[2025-08-05 13:48:18] local.INFO: DescryptData [{"contract_code":"BIDV-*************-L1","partner_code":"BIDV","partner_contract_code":"BIDV67482357","contract_amount":********,"type":2,"payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"*********","other_data":[]}],"merchant":{"merchant_id":"*********","merchant_name":"Ten Merchant","representation_name":"Ten Merchant","email":"<EMAIL>","mobile":"***********","address":"trụ sở 123","passport":"58977"}}] 
