<?php
namespace App\Modules\CollectDebt\Actions\RequestPaymentCashout\CheckPayoutManualAction;

use App\Lib\NextlendCore;
use Illuminate\Support\Arr;
use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;
use App\Modules\CollectDebt\Model\RequestPaymentCashout;
use App\Modules\CollectDebt\Requests\RequestPaymentCashout\CheckPayoutManualRequest;
use App\Modules\CollectDebt\Actions\RequestPaymentCashout\CheckPayoutBulkAction\CheckPayoutBulkAction;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;

class CheckPayoutManualAction
{
	public NextlendCore $nextlendCore;

	public function __construct(NextlendCore $nextlendCore)
	{
		$this->nextlendCore = $nextlendCore;
	}

	public function run(CheckPayoutManualRequest $request)
	{
		$guide = RequestPaymentGuide::query()->where([
			'partner_request_id' => $request->json('data.partner_request_id'),
		])->first();

		if (!$guide) {
			throw new BusinessException('Không tìm thấy thông tin yêu cầu thu hộ');
		}

		if ($guide->partner_code != $request->json('data.partner_code')) {
			throw new BusinessException('Sai thông tin đối tác');
		}
		
		$cashout = RequestPaymentCashout::query()->where([
			'request_payment_guide_id' => $guide->id,
			'partner_request_id' => $guide->partner_request_id
		])->first();

		if (!$cashout) {
			throw new BusinessException('Không tìm thấy thông tin yêu cầu cashout');
		}

		if (empty($cashout->cashout_request_id)) {
			throw new BusinessException('Yêu cầu cashout chưa được đẩy sang đối tác chi tiền');
		}

		$results = app(CheckPayoutBulkAction::class)->getCashoutDetailBulk([$cashout->partner_request_id]);
		
		if (empty($results)) {
			throw new BusinessException('Không tìm thấy thông tin yc cashout trên hệ thống đối tác');
		}


		// Process cashout
		app(CheckPayoutBulkAction::class)->processCashoutsByStatus($results);

		return $this->showResultMobile($results);
	}

	public function showResultMobile($results) {
		$first = Arr::first($results);

		$statusText = "Đang xử lý";
		
		switch ($first['status_request']) {
			case CollectDebtEnum::SERVICE_CASHOUT_SUCCESS:
				$statusText = "Báo có thành công";
				break;

			case CollectDebtEnum::SERVICE_CASHOUT_FAILED:
				$statusText = "Báo có thất bại";
				break;

			case CollectDebtEnum::SERVICE_CASHOUT_REFUND:
				$statusText = "Báo có thất bại";
				break;
			
			default:
				$statusText = "Đang xử lý";
				break;
		}
		return [
			'partner_request_id' => $first['partner_request_id'],
			'cashout_request_id' => $first['id'],
			'amount_payout' => $first['amount_request'],
			'status_request' => $first['status_request'],
			'status_request_text' => $statusText
		];
	}
} // End class