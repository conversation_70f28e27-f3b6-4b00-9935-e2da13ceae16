<?php

namespace App\Modules\CollectDebt\Actions\RequestPaymentCashout\CheckPayoutBulkAction;

use App\Lib\NextlendCore;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;
use App\Modules\CollectDebt\Model\RequestPaymentCashout;

class CheckPayoutBulkAction
{
	public int $limit = 50;

	private NextlendCore $nextlendCore;
	private int $currentTimestamp;

	public function __construct(NextlendCore $nextlendCore)
	{
		$this->nextlendCore = $nextlendCore;
		$this->currentTimestamp = now()->timestamp;
	}

	public function run()
	{
		$cashouts = $this->getCashoutsToCheck();

		if ($cashouts->isEmpty()) {
			return 'EMPTY';
		}

		$partnerRequestIds = $cashouts->pluck('partner_request_id')->toArray();
		$cashoutDetails = $this->getCashoutDetailBulk($partnerRequestIds);

		if (empty($cashoutDetails)) {
			$this->updatePendingCashouts($partnerRequestIds);
			return;
		}

		$this->processCashoutsByStatus($cashoutDetails);
	}

	private function getCashoutsToCheck()
	{
		return RequestPaymentCashout::query()
			->where('status', CollectDebtEnum::CASHOUT_STT_CREATED_CASHOUT)
			->where('time_check_payout', '<=', $this->currentTimestamp)
			->limit($this->limit)
			->get(['id', 'partner_request_id']);
	}

	public function getCashoutDetailBulk(array $partnerRequestIds): array
	{
		$this->nextlendCore->callRequest([
			'partner_request_ids' => $partnerRequestIds,
			'cashout_request_ids' => []
		], 'ColCashoutRequest_getList', 'get');

		return $this->nextlendCore->decryptData();
	}

	private function updatePendingCashouts(array $partnerRequestIds): void
	{
		RequestPaymentCashout::query()
			->whereIn('partner_request_id', $partnerRequestIds)
			->update(['time_check_payout' => now()->addHours(1)->timestamp]);
	}

	public function processCashoutsByStatus(array $cashoutDetails): void
	{
		$cashoutCollection = collect($cashoutDetails);
		// Group cashouts by their final status for batch processing
		$statusGroups = $this->groupCashoutsByStatus($cashoutCollection);

		// Process each status group
		if (!empty($statusGroups['pending'])) {
			$this->updatePendingCashouts($statusGroups['pending']);
		}

		if (!empty($statusGroups['success'])) {
			$this->updateSuccessfulCashouts($statusGroups['success']);
		}

		if (!empty($statusGroups['failed'])) {
			$this->updateFailedCashouts($statusGroups['failed']);
		}
	}

	private function groupCashoutsByStatus($cashoutCollection): array
	{
		$groups = ['pending' => [], 'success' => [], 'failed' => []];

		$mappingCashoutStatus = $this->__mappingCashoutStatus();

		foreach ($cashoutCollection as $cashout) {
			$partnerRequestId = $cashout['partner_request_id'] ?? null;

			if (!$partnerRequestId) {
				continue;
			}

			$statusRequest = $cashout['status_request'];
			$status = $cashout['status'];

			foreach ($mappingCashoutStatus as $item) {
				if ($item['status'] == $status && $item['status_request'] == $statusRequest) {
					if (!empty($item['collection_cashout_status'])) {
						if ($item['collection_cashout_status'] == CollectDebtEnum::CASHOUT_STT_SUCCESS) {
							$groups['success'][] = $partnerRequestId;
						}elseif ($item['collection_cashout_status'] == CollectDebtEnum::CASHOUT_STT_FAILED) {
							$groups['failed'][] = $partnerRequestId;
						}else {
							$groups['pending'][] = $partnerRequestId;
						}
					}else {
						// empty cot status
						$groups['pending'][] = $partnerRequestId;
					}
				}
			}
		}

		return $groups;
	}

	private function updateSuccessfulCashouts(array $partnerRequestIds): void
	{
		$updateData = [
			'status' => CollectDebtEnum::CASHOUT_STT_SUCCESS,
			'time_check_payout' => $this->currentTimestamp,
			'time_updated' => $this->currentTimestamp
		];

		RequestPaymentCashout::query()
			->whereIn('partner_request_id', $partnerRequestIds)
			->update($updateData);

		RequestPaymentGuide::query()
			->whereIn('partner_request_id', $partnerRequestIds)
			->update([
				'status_cashout' => CollectDebtEnum::RPG_STT_CASHOUT_SUCCESS_PAYOUT,
				'time_updated' => $this->currentTimestamp
			]);
	}

	private function updateFailedCashouts(array $partnerRequestIds): void
	{
		$updateData = [
			'status' => CollectDebtEnum::CASHOUT_STT_FAILED,
			'time_check_payout' => $this->currentTimestamp,
			'time_updated' => $this->currentTimestamp
		];

		RequestPaymentCashout::query()
			->whereIn('partner_request_id', $partnerRequestIds)
			->update($updateData);

		RequestPaymentGuide::query()
			->whereIn('partner_request_id', $partnerRequestIds)
			->update([
				'status_cashout' => CollectDebtEnum::RPG_STT_CASHOUT_FAILED_PAYOUT,
				'time_updated' => $this->currentTimestamp
			]);
	}

	private function __mappingCashoutStatus() {
		$statusData = [
			// Status: 1. Mới yêu cầu
			['status' => 1, 'status_request' => 1, 'text' => 'Đối tác đang xử lý báo có'],
			['status' => 1, 'status_request' => 2, 'text' => 'X'],
			['status' => 1, 'status_request' => 3, 'text' => 'X'],
			['status' => 1, 'status_request' => 4, 'text' => 'X'],
			['status' => 1, 'status_request' => 5, 'text' => 'X'],
			['status' => 1, 'status_request' => 6, 'text' => 'X'],
			['status' => 1, 'status_request' => 7, 'text' => 'X'],
			['status' => 1, 'status_request' => 8, 'text' => 'X'],

			// Status: 2. Đã duyệt
			['status' => 2, 'status_request' => 1, 'text' => 'Đối tác đang xử lý báo có'],
			['status' => 2, 'status_request' => 2, 'text' => 'Đối tác đang xử lý báo có'],
			['status' => 2, 'status_request' => 3, 'text' => 'Đối tác đang xử lý báo có'],
			['status' => 2, 'status_request' => 4, 'text' => 'Đối tác đang xử lý báo có'],
			['status' => 2, 'status_request' => 5, 'text' => 'X'],
			['status' => 2, 'status_request' => 6, 'text' => 'X'],
			['status' => 2, 'status_request' => 7, 'text' => 'X'],
			['status' => 2, 'status_request' => 8, 'text' => 'X'],

			// Status: 3. Đã giải ngân
			['status' => 3, 'status_request' => 1, 'text' => 'X'],
			['status' => 3, 'status_request' => 2, 'text' => 'X'],
			['status' => 3, 'status_request' => 3, 'text' => 'X'],
			['status' => 3, 'status_request' => 4, 'text' => 'Đối tác đang xử lý báo có'],
			['status' => 3, 'status_request' => 5, 'text' => 'Báo có thành công', 'collection_cashout_status' => CollectDebtEnum::CASHOUT_STT_SUCCESS],
			['status' => 3, 'status_request' => 6, 'text' => 'Báo có thất bại', 'collection_cashout_status' => CollectDebtEnum::CASHOUT_STT_FAILED],
			['status' => 3, 'status_request' => 7, 'text' => 'Báo có thất bại', 'collection_cashout_status' => CollectDebtEnum::CASHOUT_STT_FAILED],
			['status' => 3, 'status_request' => 8, 'text' => 'Báo có thất bại', 'collection_cashout_status' => CollectDebtEnum::CASHOUT_STT_FAILED],

			// Status: 4. Đã từ chối
			['status' => 4, 'status_request' => 1, 'text' => 'Báo có thất bại', 'collection_cashout_status' => CollectDebtEnum::CASHOUT_STT_FAILED],
			['status' => 4, 'status_request' => 2, 'text' => 'X'],
			['status' => 4, 'status_request' => 3, 'text' => 'X'],
			['status' => 4, 'status_request' => 4, 'text' => 'Báo có thất bại', 'collection_cashout_status' => CollectDebtEnum::CASHOUT_STT_FAILED],
			['status' => 4, 'status_request' => 5, 'text' => 'Báo có thất bại', 'collection_cashout_status' => CollectDebtEnum::CASHOUT_STT_FAILED],
			['status' => 4, 'status_request' => 6, 'text' => 'Báo có thất bại', 'collection_cashout_status' => CollectDebtEnum::CASHOUT_STT_FAILED],
			['status' => 4, 'status_request' => 7, 'text' => 'Báo có thất bại', 'collection_cashout_status' => CollectDebtEnum::CASHOUT_STT_FAILED],
			['status' => 4, 'status_request' => 8, 'text' => 'X'],

			// Status: 5. Thất bại
			['status' => 5, 'status_request' => 1, 'text' => 'Báo có thất bại', 'collection_cashout_status' => CollectDebtEnum::CASHOUT_STT_FAILED],
			['status' => 5, 'status_request' => 2, 'text' => 'X'],
			['status' => 5, 'status_request' => 3, 'text' => 'Báo có thất bại', 'collection_cashout_status' => CollectDebtEnum::CASHOUT_STT_FAILED],
			['status' => 5, 'status_request' => 4, 'text' => 'Báo có thất bại', 'collection_cashout_status' => CollectDebtEnum::CASHOUT_STT_FAILED],
			['status' => 5, 'status_request' => 5, 'text' => 'Báo có thất bại', 'collection_cashout_status' => CollectDebtEnum::CASHOUT_STT_FAILED],
			['status' => 5, 'status_request' => 6, 'text' => 'Báo có thất bại', 'collection_cashout_status' => CollectDebtEnum::CASHOUT_STT_FAILED],
			['status' => 5, 'status_request' => 7, 'text' => 'Báo có thất bại', 'collection_cashout_status' => CollectDebtEnum::CASHOUT_STT_FAILED],
			['status' => 5, 'status_request' => 8, 'text' => 'X'],

			// Status: 6. Từ chối
			['status' => 6, 'status_request' => 1, 'text' => 'Báo có thất bại', 'collection_cashout_status' => CollectDebtEnum::CASHOUT_STT_FAILED],
			['status' => 6, 'status_request' => 2, 'text' => 'X'],
			['status' => 6, 'status_request' => 3, 'text' => 'X'],
			['status' => 6, 'status_request' => 4, 'text' => 'X'],
			['status' => 6, 'status_request' => 5, 'text' => 'X'],
			['status' => 6, 'status_request' => 6, 'text' => 'X'],
			['status' => 6, 'status_request' => 7, 'text' => 'X'],
			['status' => 6, 'status_request' => 8, 'text' => 'X']
		];

		return $statusData;
	}
} // End class