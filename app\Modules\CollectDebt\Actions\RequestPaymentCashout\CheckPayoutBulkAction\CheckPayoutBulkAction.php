<?php

namespace App\Modules\CollectDebt\Actions\RequestPaymentCashout\CheckPayoutBulkAction;

use App\Lib\NextlendCore;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;
use App\Modules\CollectDebt\Model\RequestPaymentCashout;

class CheckPayoutBulkAction
{
	public int $limit = 50;

	private NextlendCore $nextlendCore;
	private int $currentTimestamp;

	public function __construct(NextlendCore $nextlendCore)
	{
		$this->nextlendCore = $nextlendCore;
		$this->currentTimestamp = now()->timestamp;
	}

	public function run()
	{
		$cashouts = $this->getCashoutsToCheck();

		if ($cashouts->isEmpty()) {
			return 'EMPTY';
		}

		$partnerRequestIds = $cashouts->pluck('partner_request_id')->toArray();
		$cashoutDetails = $this->getCashoutDetailBulk($partnerRequestIds);

		if (empty($cashoutDetails)) {
			$this->updatePendingCashouts($partnerRequestIds);
			return;
		}

		$this->processCashoutsByStatus($cashoutDetails);
	}

	private function getCashoutsToCheck()
	{
		return RequestPaymentCashout::query()
			->where('status', CollectDebtEnum::CASHOUT_STT_CREATED_CASHOUT)
			->where('time_check_payout', '<=', $this->currentTimestamp)
			->limit($this->limit)
			->get(['id', 'partner_request_id']);
	}

	public function getCashoutDetailBulk(array $partnerRequestIds): array
	{
		$this->nextlendCore->callRequest([
			'partner_request_ids' => $partnerRequestIds,
			'cashout_request_ids' => []
		], 'ColCashoutRequest_getList', 'get');

		return $this->nextlendCore->decryptData();
	}

	private function updatePendingCashouts(array $partnerRequestIds): void
	{
		RequestPaymentCashout::query()
			->whereIn('partner_request_id', $partnerRequestIds)
			->update(['time_check_payout' => now()->addHours(1)->timestamp]);
	}

	public function processCashoutsByStatus(array $cashoutDetails): void
	{
		$cashoutCollection = collect($cashoutDetails);
		// Group cashouts by their final status for batch processing
		$statusGroups = $this->groupCashoutsByStatus($cashoutCollection);

		// Process each status group
		if (!empty($statusGroups['pending'])) {
			$this->updatePendingCashouts($statusGroups['pending']);
		}

		if (!empty($statusGroups['success'])) {
			$this->updateSuccessfulCashouts($statusGroups['success']);
		}

		if (!empty($statusGroups['failed'])) {
			$this->updateFailedCashouts($statusGroups['failed']);
		}
	}

	private function groupCashoutsByStatus($cashoutCollection): array
	{
		$groups = ['pending' => [], 'success' => [], 'failed' => []];

		foreach ($cashoutCollection as $cashout) {
			$statusRequest = $cashout['status_request'] ?? null;
			$partnerRequestId = $cashout['partner_request_id'] ?? null;

			if (!$partnerRequestId) {
				continue;
			}

			if (!CollectDebtEnum::isFinalCashoutServiceState($statusRequest)) {
				$groups['pending'][] = $partnerRequestId;
			} elseif ($statusRequest === CollectDebtEnum::SERVICE_CASHOUT_SUCCESS) {
				$groups['success'][] = $partnerRequestId;
			} elseif (in_array($statusRequest, [
				CollectDebtEnum::SERVICE_CASHOUT_FAILED,
				CollectDebtEnum::SERVICE_CASHOUT_REFUND,
			])) {
				$groups['failed'][] = $partnerRequestId;
			}
		}

		return $groups;
	}

	private function updateSuccessfulCashouts(array $partnerRequestIds): void
	{
		$updateData = [
			'status' => CollectDebtEnum::CASHOUT_STT_SUCCESS,
			'time_check_payout' => $this->currentTimestamp,
			'time_updated' => $this->currentTimestamp
		];

		RequestPaymentCashout::query()
			->whereIn('partner_request_id', $partnerRequestIds)
			->update($updateData);

		RequestPaymentGuide::query()
			->whereIn('partner_request_id', $partnerRequestIds)
			->update([
				'status_cashout' => CollectDebtEnum::RPG_STT_CASHOUT_SUCCESS_PAYOUT,
				'time_updated' => $this->currentTimestamp
			]);
	}

	private function updateFailedCashouts(array $partnerRequestIds): void
	{
		$updateData = [
			'status' => CollectDebtEnum::CASHOUT_STT_FAILED,
			'time_check_payout' => $this->currentTimestamp,
			'time_updated' => $this->currentTimestamp
		];

		RequestPaymentCashout::query()
			->whereIn('partner_request_id', $partnerRequestIds)
			->update($updateData);

		RequestPaymentGuide::query()
			->whereIn('partner_request_id', $partnerRequestIds)
			->update([
				'status_cashout' => CollectDebtEnum::RPG_STT_CASHOUT_FAILED_PAYOUT,
				'time_updated' => $this->currentTimestamp
			]);
	}
} // End class