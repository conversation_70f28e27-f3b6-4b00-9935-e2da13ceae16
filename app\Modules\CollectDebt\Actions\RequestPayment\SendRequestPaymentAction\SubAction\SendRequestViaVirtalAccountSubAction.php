<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\SendRequestPaymentAction\SubAction;

use App\Lib\Helper;
use Illuminate\Support\Facades\Log;
use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\RequestPaymentLog;
use App\Modules\CollectDebt\Model\RequestPaymentAction;
use App\Modules\CollectDebt\DTOs\RequestPayment\SendVirtualAccountRequestDTO;
use App\Modules\CollectDebtGateway\Repositories\Connections\NextLendServiceConnection;

class SendRequestViaVirtalAccountSubAction
{
	public NextLendServiceConnection $connection;

	public function __construct(NextLendServiceConnection $connection)
	{
		$this->connection = $connection;
	}

	/**
	 * array:8 [
			"Fnc" => "createVAOn"
			"Version" => "1.0"
			"ChannelCode" => "THUHO"
			"RespCode" => "00"
			"data" => "{"error_code":"00","error_message":"Thành công","checksum":"304db87e1e9f049935c298af080edae1","data":{"mcRequestId":"THUHO-NLTHVA2025070817303873","vaNextpayNumber":"NPHWGBG8NSTLYPX","vaBankNumber":"962NPD3551623231180","qrCode":"00020101021238630010A000000727013300069704180119962NPD35516232311800208QRIBFTTA53037045405150005802VN62350831ODONJIAZ4KSYXNP Thanh toan TCNL63045a50","qrImage":"","status":"ACTIVED","channelCode":"BIDV","vaReference":"ODONJIAZ4KSYXNP","transferDesc":"ODONJIAZ4KSYXNP Thanh toan TCNL"}}"
			"total" => 0
			"Checksum" => "ed93509f45eb2aa93c28aab1d0a75d51"
			"Description" => "success"
		]
	 */
	public function run(RequestPayment $requestPayment)
	{
		$dto = SendVirtualAccountRequestDTO::fromRequestPayment($requestPayment);
		$params = $dto->toArray();

		Log::info('SendRequestVA', $params);

		$sendRequestResult = $this->connection->callRequest($params, 'createVAOn', 'POST');

		Log::info('SendRequestVAResult', $sendRequestResult);

		$requestPaymentLog = RequestPaymentLog::query()->forceCreate([
			'contract_code' => $requestPayment->contract_code,
			'partner_request_id' => $requestPayment->partner_request_id,
			'func' => 'send_va_request',
			'request' => json_encode($params),
			'response' => json_encode($sendRequestResult),
		]);
		
		if (!empty($sendRequestResult['RespCode']) && $sendRequestResult['RespCode'] == '00') {
			$data = json_decode($sendRequestResult['data'], true);

			if (!empty($data['data']['vaNextpayNumber'])) {
				$wasUpdated = $requestPayment->update([
					'partner_transaction_id' => $data['data']['vaNextpayNumber'],
					'status' => CollectDebtEnum::RQ_STT_APPROVED,
					'status_payment' => CollectDebtEnum::RP_STT_PAYMENT_SENDED,
					'time_updated' => now()->timestamp,
					'time_sended' => now()->timestamp,
					'updated_by' => Helper::getSystemUser(),
					'sended_by' => Helper::getSystemUser(),
				]);

				if (!$wasUpdated) {
					throw new BusinessException('Lỗi không cập nhật trạng thái lệnh trích');
				}

				@RequestPaymentAction::query()->forceCreate([
					'request_payment_id' => $requestPayment->id,
					'contract_code' => $requestPayment->contract_code,
					'action' => 'CREATE_VA',
					'data' => json_encode($sendRequestResult),
					'status' => CollectDebtEnum::RPA_STATUS_DONE,
					'time_created' => now()->timestamp,
					'time_updated' => now()->timestamp,
					'created_by' => Helper::getSystemUser(),
					'updated_by' => Helper::getSystemUser(),
					'description' => 'Thông tin khi tạo lệnh trích VA thành công',
					'tries' => 1,
				]);

				return $requestPayment;
			}
		}

		throw new BusinessException('Lỗi không có thông tin tạo kết quả VA');
	}
} // End class
