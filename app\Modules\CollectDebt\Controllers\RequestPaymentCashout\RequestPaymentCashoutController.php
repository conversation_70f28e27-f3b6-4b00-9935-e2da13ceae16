<?php

namespace App\Modules\CollectDebt\Controllers\RequestPaymentCashout;

use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Resources\RequestPaymentCashoutResourceCollection;
use App\Modules\CollectDebt\Requests\RequestPaymentCashout\CheckPayoutManualRequest;
use App\Modules\CollectDebt\Requests\RequestPaymentCashout\CheckPayoutResultRequest;
use App\Modules\CollectDebt\Requests\RequestPaymentCashout\GetAllCashoutRecordRequest;
use App\Modules\CollectDebt\Actions\RequestPaymentCashout\PushCashoutAction\PushCashoutAction;
use App\Modules\CollectDebt\Actions\RequestPaymentCashout\CreateCashoutAction\CreateCashoutAction;
use App\Modules\CollectDebt\Actions\RequestPaymentCashout\CheckPayoutBulkAction\CheckPayoutBulkAction;
use App\Modules\CollectDebt\Actions\RequestPaymentCashout\CheckPayoutManualAction\CheckPayoutManualAction;
use App\Modules\CollectDebt\Actions\RequestPaymentCashout\GetAllCashoutRecordAction\GetAllCashoutRecordAction;

class RequestPaymentCashoutController extends Controller
{
	public function CreateCashout()
	{
		$result = app(CreateCashoutAction::class)->run();
		return $this->successResponse($result);
	}

	public function PushCashout()
	{
		$result = app(PushCashoutAction::class)->run();
		return $this->successResponse($result);
	}

	public function CheckPayoutBulk()
	{
		$result = app(CheckPayoutBulkAction::class)->run();
		return $this->successResponse($result);
	}

	public function GetAllCashoutRecord(GetAllCashoutRecordRequest $request)
	{
		$cashoutsPaginated = app(GetAllCashoutRecordAction::class)->run($request);
		$resource = new RequestPaymentCashoutResourceCollection($cashoutsPaginated);
		$response = $resource->toArray($request);
		return $this->successResponse($response);
	}

	public function CheckPayoutManual(CheckPayoutManualRequest $request)
	{
		$result = app(CheckPayoutManualAction::class)->run($request);
		return $this->successResponse($result);
	}
} // End class