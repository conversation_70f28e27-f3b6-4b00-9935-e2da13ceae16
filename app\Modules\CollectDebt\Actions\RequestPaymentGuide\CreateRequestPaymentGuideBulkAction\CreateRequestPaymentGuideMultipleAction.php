<?php

namespace App\Modules\CollectDebt\Actions\RequestPaymentGuide\CreateRequestPaymentGuideBulkAction;

use Carbon\Carbon;
use App\Lib\NextlendCore;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;
use App\Modules\CollectDebt\Model\RequestPaymentGuideMerchant;
use App\Modules\CollectDebt\Requests\RequestPaymentGuide\CreateRequestPaymentGuideMultipleRequest;

class CreateRequestPaymentGuideMultipleAction
{
	private array $__partnerRequestIdCreated = [];

	private array $__results = [];

	public NextlendCore $nextlendCore;

	public function __construct(NextlendCore $nextlendCore)
	{
		$this->nextlendCore = $nextlendCore;
	}

	public function run(CreateRequestPaymentGuideMultipleRequest $request)
	{
		Log::info("Portal Request: ", $request->json('data'));
		
		$listContractCode = Arr::pluck($request->json('data'), 'contract_code');
		
		$r = $this->nextlendCore->callRequest(
			['contract_code' => $listContractCode],
			'ThuhoBidvSupport_getContractsReadyForCollection',
			'post'
		);

		$decryptData = $r->decryptData();
		$listHopDongDapUngDieuKienThuHo = Arr::pluck($decryptData, 'contract_code');

		if (empty($listHopDongDapUngDieuKienThuHo)) {
			throw new BusinessException('Không có hợp đồng nào đáp ứng được điều kiện thu hộ');
		}

		$partnerRequestIds = [];
		$skippedItems = [];

		foreach ($request->json('data') as $index => $item) {
			$contractCode = $item['contract_code'];

			// Hđ truyền lên thì mà không đáp ứng được thì không cho tạo yc
			if (!in_array($contractCode, $listHopDongDapUngDieuKienThuHo)) {
				$this->__results[] = [
					'unique_id' => $item['unique_id'] ?? $item['partner_contract_code'],
					'success' => false,
					'reason' => 'Không tồn tại khoản ứng trong hệ thống hợp đồng'
				];
				continue;
			}

			DB::beginTransaction();
			try {
				// Kiểm tra xem contract_code đã tồn tại với status != 6 chưa
				$existingGuide = RequestPaymentGuide::query()
					->where([
						['contract_code', '=', $contractCode],
						['status', '!=', CollectDebtEnum::REQUEST_PAYMENT_GUIDE_NOTICE_PARTNER]
					]);

				if (!empty($item['partner_request_id'])) {
					$existingGuide = $existingGuide->orWhere('partner_request_id', $item['partner_request_id']);
				}
				
				$existingGuide = $existingGuide->first();

				if ($existingGuide) {
					// Nếu đã tồn tại với status != 6, bỏ qua item này
					$skippedItems[] = $item['partner_request_id'];

					$this->__results[] = [
						'unique_id' => $item['unique_id'] ?? $item['partner_contract_code'],
						'success' => false,
						'reason' => 'Yêu cầu thu hộ đã tồn tại hoặc hợp đồng đang có yêu cầu thu nợ chưa xử lý xong'
					];
					DB::commit();
					continue;
				}

				$p = [
					'profile_id' => $item['profile_id'],
					'partner_code' => Str::of($item['partner_code'])->trim(),
					'contract_code' => Str::of($item['contract_code'])->trim(),
					'partner_request_id' => $item['partner_request_id'] ?? '',
					'amount' => $item['amount'],
					'type' => $item['type'],
					'created_by' => $item['created_by'],
					'requested_by' => $item['requested_by'],
					'time_requested' => Carbon::parse($item['requested_at'])->timestamp,
					'time_created' => now()->timestamp,
					'payment_guide' => json_encode($item['payment_guide']),
					'is_required_notify' => intval($item['is_required_notify']),
					'partner_contract_code' => $item['partner_contract_code'] ?? '',
					'contract_amount' => $item['contract_amount'] ?? 0,
					'partner_request_account' => $item['partner_request_account'],
					'partner_request_holdername' => $item['partner_request_holdername'] ?? '',
					'partner_request_bank_code' => $item['partner_request_bank_code'] ?? '',
					'is_required_cashout' => $item['partner_code'] == 'BIDV' ? 1 : 0,
				];


				if (!empty($item['expired_at'])) {
					$p['time_expired'] = Carbon::parse($item['expired_at'])->timestamp;
				}

				if (empty($item['expired_at'])) {
					$expiredToDay = today()->setTime(23, 00);
					$expiredTomorrow = today()->addDay()->setTime(23, 00);

					$p['time_expired'] = $expiredTomorrow->timestamp;

					if (now()->lt($expiredToDay)) {
						$p['time_expired'] = $expiredToDay->timestamp;
					}
				}

				if ($item['is_approved']) {
					$p['status'] = CollectDebtEnum::REQUEST_PAYMENT_GUIDE_APPROVED;
				}

				$requestPaymentGuide = RequestPaymentGuide::query()->firstWhere([
					'contract_code' => $p['contract_code'],
					'partner_request_id' => $p['partner_request_id'],
				]);

				if (!$requestPaymentGuide) {
					$requestPaymentGuide = RequestPaymentGuide::query()->forceCreate($p);

					if (empty($p['partner_request_id'])) {
						$requestPaymentGuide->partner_request_id = sprintf(
							'NL%s%s%s', 
							$requestPaymentGuide->partner_code,
							date('ymd'), 
							$requestPaymentGuide->id
						);

						$r = $requestPaymentGuide->save();
						if (!$r) {
							throw new BusinessException('Lỗi không sinh được partner_request_id');
						}
					}
					
					$this->__partnerRequestIdCreated[] = $requestPaymentGuide->partner_request_id;
				}

				if (!$requestPaymentGuide) {
					throw new BusinessException('Tạo lệnh yêu cầu thu hộ không thành công');
				}

				$paramsMc = Arr::only($item['merchant'], [
					'merchant_id',
					'merchant_name',
					'representation_name',
					'email',
					'mobile',
					'address',
					'passport',
					'request_payment_guide_id' => $requestPaymentGuide->id,
				]);

				$merchant = RequestPaymentGuideMerchant::query()->firstOrCreate([
					'request_payment_guide_id' => $requestPaymentGuide->id,
				], $paramsMc);

				if (!$merchant) {
					throw new BusinessException('Tạo merchant không thành công');
				}

				$this->__results[] = [
					'unique_id' => $item['unique_id'] ?? $item['partner_contract_code'],
					'success' => true,
					'reason' => 'Tạo yc thu hộ thành công'
				];
				DB::commit();
			} catch (\Throwable $th) {
				DB::rollBack();
				$this->__results[] = [
					'unique_id' => $item['unique_id'] ?? $item['partner_contract_code'],
					'success' => false,
					'reason' => $th->getMessage()
				];
				throw new BusinessException($th->getMessage());
			}
		} // End foreach

		return [
			'success_partner_request_id' => $this->__partnerRequestIdCreated,
			'skipped_partner_request_id' => $skippedItems,
			'total_processed' => count($partnerRequestIds),
			'total_skipped' => count($skippedItems),
			'results' => $this->__results
		];
	}
} // End class
