<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\SendRequestPaymentAction\SubAction;

use App\Lib\Helper;
use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Model\RequestPaymentLog;
use App\Modules\CollectDebt\DTOs\RequestPayment\SendRequestPaymentDTO;
use App\Modules\CollectDebtGateway\Repositories\CollectDebtGatewayRepository;
use App\Modules\CollectDebt\Actions\RequestPayment\SendRequestPaymentAction\SubAction\XuLyLenhTrichBiTrungSubAction;
use App\Modules\CollectDebt\Actions\RequestPayment\SendRequestPaymentAction\SubAction\XuLyLenhTrichThanhCongSubAction;
use App\Modules\CollectDebt\Actions\RequestPayment\SendRequestPaymentAction\SubAction\XuLyLenhTrichKhongCoMaLoiSubAction;

class SendRequestViaMposSubAction
{
	public CollectDebtGatewayRepository $mposCollectDebtGatewayRepo;

	public function __construct(CollectDebtGatewayRepository $mposCollectDebtGatewayRepo)
	{
		$this->mposCollectDebtGatewayRepo = $mposCollectDebtGatewayRepo;
	}

  public function run(RequestPayment $requestPayment)
  {
		$dto = SendRequestPaymentDTO::fromRequestPayment($requestPayment);
		$params = $dto->toArray();

		$requestPaymentLog = RequestPaymentLog::query()->forceCreate([
			'contract_code' => $requestPayment->contract_code,
			'partner_request_id' => $requestPayment->partner_request_id,
			'func' => 'send_mpos_request',
			'request' => json_encode($params),
		]);
		
		logger()->info('ParamGuiLenhTrich:', $params);
    try {
			$sendDebtResult = $this->mposCollectDebtGatewayRepo->sendCollectDebtCommand($params);

			// lưu log
			$requestPaymentLog->response = json_encode($sendDebtResult);
			$requestPaymentLog->save();

			logger()->info('ResultGuiTrichMpos', $sendDebtResult);


      // không có mã lỗi
      if (empty($sendDebtResult) || empty($sendDebtResult['errorCode'])) {
        return app(XuLyLenhTrichKhongCoMaLoiSubAction::class)->run($requestPayment);
      }

      if (!empty($sendDebtResult['errorCode'])) {
        switch ($sendDebtResult['errorCode']) {
          // thành công
          case '00':
            return app(XuLyLenhTrichThanhCongSubAction::class)->run($requestPayment, $sendDebtResult);

          // lỗi trùng
          case '-46001':
            return app(XuLyLenhTrichBiTrungSubAction::class)->run($requestPayment);

          // có mã lỗi, nhưng chưa được define rõ ràng. Đối xử như không có mã lỗi
          default:
						return app(XuLyLenhTrichKhongCoMaLoiSubAction::class)->run($requestPayment);
        }
      }
    } catch (\Throwable $th) {
			$requestPaymentLog->errors = json_encode(['file' => $th->getFile(), 'line' => $th->getLine(), 'message' => $th->getMessage()]);
			$requestPaymentLog->save();
    }
  } // End method
} // End class