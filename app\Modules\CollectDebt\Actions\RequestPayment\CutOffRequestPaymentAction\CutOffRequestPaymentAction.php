<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\CutOffRequestPaymentAction;

use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebtGateway\Repositories\CollectDebtGatewayRepository;
use App\Modules\CollectDebt\DTOs\RequestPaymentReceipt\CreateRequestPaymentReceiptDTO;
use App\Modules\CollectDebt\Actions\RequestPayment\CheckRequestPaymentAction\CheckRequestPaymentAction;
use App\Modules\CollectDebt\Actions\RequestPayment\CancelRequestPaymentAction\CancelRequestPaymentAction;
use App\Modules\CollectDebt\Actions\RequestPayment\CheckRequestPaymentAction\CheckRequestPaymentVAAction;
use App\Modules\CollectDebt\Actions\RequestPaymentReceipt\CreateRequestPaymentReceiptAction\CreateRequestPaymentReceiptAction;

class CutOffRequestPaymentAction
{
	// Constants
	private const MAX_PROCESSING_ITERATIONS = 40;
	private const EMPTY_RESULT = 'EMPTY';
	
	// Debt status constants
	private const DEBT_STATUS_CANCEL = 'CANCEL';
	private const DEBT_STATUS_NOT_FOUND = 'NOTFOUND';
	private const DEBT_STATUS_PENDING = 'PENDING';
	private const DEBT_STATUS_EXPIRED = 'EXPIRED';
	private const DEBT_STATUS_TIMEOUT = 'TIMEOUT';
	private const DEBT_STATUS_APPROVED = 'APPROVED';

	private array $processedIds = [];
	private array $excludedIds = [];

	public CollectDebtGatewayRepository $mposCollectDebtGatewayRepo;

	public function __construct(CollectDebtGatewayRepository $mposCollectDebtGatewayRepo)
	{
		$this->mposCollectDebtGatewayRepo = $mposCollectDebtGatewayRepo;
	}


	public function run()
	{
		try {
			for ($i = 1; $i < self::MAX_PROCESSING_ITERATIONS; $i++) {
				$requestPayment = $this->processCutOffRequestPayment();

				if ($requestPayment === self::EMPTY_RESULT) {
					$this->processedIds[] = 'EMPTY';
					break;
				}

				$this->processedIds[] = $requestPayment->partner_request_id;
			}
		} catch (\Throwable $th) {
			throw $th;
		}

		return $this->processedIds;
	}

	public function processCutOffRequestPayment()
	{
		$requestPayment = $this->findExpiredRequestPayment();

		if (!$requestPayment) {
			return self::EMPTY_RESULT;
		}

		$this->excludedIds[] = $requestPayment->id;

		// Nếu kênh trích là mpos
		if ($requestPayment->payment_method_code === 'MPOS') {
			return $this->processMposPayment($requestPayment);
		}

		// Nếu kênh trích là VA
		if ($requestPayment->payment_method_code === 'VIRTUALACCOUNT') {
			return $this->processVirtualAccountPayment($requestPayment);
		}

		return $requestPayment;
	}

	public function handleEmptyPartnerTransactionId(RequestPayment $requestPayment)
	{
		$dto = CreateRequestPaymentReceiptDTO::fromRequestPayment($requestPayment, 0, CollectDebtEnum::RP_RECEIPT_APPROVED);
		$dto->description = 'Cutoff lệnh trích quá khứ không sang được đối tác trích nợ';
		$dto->partner_transaction_id = "CUTOFF_ERROR_" . $requestPayment->id;
		return app(CreateRequestPaymentReceiptAction::class)->run($dto);
	}

	/**
	 * Tìm request payment đã hết hạn và chưa có receipt
	 */
	private function findExpiredRequestPayment(): ?RequestPayment
	{
		$query = RequestPayment::query()
			->where('status_payment', '!=', CollectDebtEnum::RP_STT_PAYMENT_RECEIVED)
			->where('time_expired', '<=', now()->timestamp)
			->doesntHave('requestPaymentReceipt');

		if (!empty($this->excludedIds)) {
			$query->whereNotIn('id', $this->excludedIds);
		}

		return $query->orderByRaw(request('orderByRaw', 'id asc'))->first();
	}

	/**
	 * Xử lý payment method MPOS
	 */
	private function processMposPayment(RequestPayment $requestPayment)
	{
		// Lỗi gì đó mà không thể đẩy lệnh trích sang đối tác
		if (empty($requestPayment->partner_transaction_id)) {
			return $this->handleEmptyPartnerTransactionId($requestPayment);
		}

		$checkResult = app(CheckRequestPaymentAction::class)->executeCheck($requestPayment);
		$debtStatus = app(CheckRequestPaymentAction::class)->getDebitCmdStatus($checkResult);

		return $this->handleMposDebtStatus($requestPayment, $debtStatus, $checkResult);
	}

	/**
	 * Xử lý các trạng thái debt status cho MPOS
	 */
	private function handleMposDebtStatus(RequestPayment $requestPayment, string $debtStatus, array $checkResult)
	{
		switch ($debtStatus) {
			// Hủy thành công -> giải phóng lệnh trích
			case self::DEBT_STATUS_CANCEL:
			case self::DEBT_STATUS_NOT_FOUND:
				return $this->createApprovedReceipt($requestPayment, 0);

			// chưa có kết quả trích - Không có thông tin -> gọi hủy để kết thúc
			case self::DEBT_STATUS_PENDING:
				$cancelResult = app(CancelRequestPaymentAction::class)->run($requestPayment);
				if (CancelRequestPaymentAction::isCancelSuccess($cancelResult)) {
					return $this->createApprovedReceipt($requestPayment, 0);
				}
				return $requestPayment;

			// lệnh hết hạn -> gọi cancel và không quan tâm cancel được hay không
			case self::DEBT_STATUS_EXPIRED:
				app(CancelRequestPaymentAction::class)->run($requestPayment);
				return $this->createApprovedReceipt($requestPayment, 0);

			// gọi sang mpos có vấn đề gì đó mà không được
			case self::DEBT_STATUS_TIMEOUT:
				$cancelResult = app(CancelRequestPaymentAction::class)->run($requestPayment);
				if (CancelRequestPaymentAction::isCancelSuccess($cancelResult)) {
					return $this->createApprovedReceipt($requestPayment, 0);
				}
				return $requestPayment;

			// trích thành công -> giải phóng lệnh trích
			case self::DEBT_STATUS_APPROVED:
				$amount = $checkResult['data']['data']['debtRecoveryAmount'];
				return $this->createApprovedReceipt($requestPayment, $amount);

			// những trường hợp khác, gọi cancel rồi bắt trên kết quả
			default:
				$cancelResult = app(CancelRequestPaymentAction::class)->run($requestPayment);
				if (CancelRequestPaymentAction::isCancelSuccess($cancelResult)) {
					return $this->createApprovedReceipt($requestPayment, 0);
				}
				return $requestPayment;
		}
	}

	/**
	 * Xử lý payment method Virtual Account
	 */
	private function processVirtualAccountPayment(RequestPayment $requestPayment)
	{
		$checkVaResult = app(CheckRequestPaymentVAAction::class)->executeCheck($requestPayment);
		$amountSuccess = 0;
		$description = 'Cutoff lệnh VA không có thông tin';

		$isPaidVA = app(CheckRequestPaymentVAAction::class)->isPaidVA($checkVaResult);

		if ($isPaidVA) {
			$amountSuccess = $requestPayment->amount_request;
			$description = "";
		}

		$dto = CreateRequestPaymentReceiptDTO::fromRequestPayment(
			$requestPayment,
			$amountSuccess,
			CollectDebtEnum::RP_RECEIPT_APPROVED
		);

		$dto->description = $description;
		return app(CreateRequestPaymentReceiptAction::class)->run($dto);
	}

	/**
	 * Tạo receipt đã được approve
	 */
	private function createApprovedReceipt(RequestPayment $requestPayment, int $amount)
	{
		$dto = CreateRequestPaymentReceiptDTO::fromRequestPayment($requestPayment, $amount, CollectDebtEnum::RP_RECEIPT_APPROVED);
		return app(CreateRequestPaymentReceiptAction::class)->run($dto);
	}
} // End class