# API Documentation: CreateRequestPaymentGuideMultiple

## Tổng quan
API này được sử dụng để tạo nhiều yêu cầu thu hộ (payment guide) cùng một lúc cho hệ thống thu nợ.

## Thông tin cơ bản
- **URL**: `POST /CreateRequestPaymentGuideMultiple`
- **Base URL**: `https://gateway-dev.nextlend.vn/api-collection`
- **Content-Type**: `application/json`
- **Middleware**: `nextlend.collection` (bao gồm checksum validation)

## Authentication & Security
API sử dụng checksum để xác thực request:
- **checksum**: MD5 hash được tạo từ: `data + version + channel_code + time_request + secret_key`
- **time_request**: Unix timestamp
- **version**: Phiên bản API (ví dụ: "1.0")
- **channel_code**: Mã kênh (ví dụ: "WEBBACKEND_PARTNER")

## Request Structure

### Headers
```
Content-Type: application/json
```

### Body Parameters

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `channel_code` | string | Yes | Mã kênh (WEBBACKEND_PARTNER) |
| `data` | array | Yes | Mảng các yêu cầu thu hộ |
| `time_request` | integer | Yes | Unix timestamp |
| `version` | string | Yes | Phiên bản API |
| `checksum` | string | Yes | MD5 checksum để xác thực |

### Data Array Structure
Mỗi phần tử trong mảng `data` có cấu trúc:

| Field | Type | Required | Validation | Description |
|-------|------|----------|------------|-------------|
| `contract_code` | string | Yes | max:50 | Mã hợp đồng |
| `partner_code` | string | Yes | in:NEXTLEND,TNEX,BIDV,VIETCREDIT,VPBANK | Mã đối tác |
| `partner_contract_code` | string | No | | Mã hợp đồng của đối tác |
| `contract_amount` | integer | No | | Số tiền hợp đồng |
| `type` | integer | Yes | in:1,2 | Loại yêu cầu |
| `amount` | integer | Yes | min:1, max:********** | Số tiền thu hộ |
| `partner_request_id` | string | Conditional | max:50, regex:/^[A-Z0-9]+$/ | Mã yêu cầu đối tác (bắt buộc trừ BIDV) |
| `created_by` | string | Yes | max:255 | Người tạo |
| `requested_by` | string | Yes | max:255 | Người yêu cầu |
| `requested_at` | string | Yes | Y-m-d H:i:s | Thời gian yêu cầu |
| `expired_at` | string | No | Y-m-d H:i:s, after:+8 hours | Thời gian hết hạn |
| `is_approved` | boolean | Yes | | Trạng thái duyệt |
| `profile_id` | integer | Yes | min:0 | ID hồ sơ |
| `is_required_notify` | boolean | Yes | | Có cần thông báo |
| `partner_request_bank_code` | string | No | | Mã ngân hàng đối tác |
| `partner_request_holdername` | string | No | | Tên chủ tài khoản |
| `partner_request_account` | string | Conditional | max:255 | Tài khoản đối tác (bắt buộc với BIDV) |

### Payment Guide Structure
| Field | Type | Required | Validation | Description |
|-------|------|----------|------------|-------------|
| `payment_method_code` | string | Yes | in:MPOS,VIRTUALACCOUNT,IB_OFF | Phương thức thanh toán |
| `payment_channel_code` | string | Yes | in:MPOS,VIRTUALACCOUNT,IB_OFF | Kênh thanh toán |
| `payment_account_id` | string | Yes | | ID tài khoản thanh toán |
| `other_data` | array | Yes | | Dữ liệu khác |

### Merchant Structure
| Field | Type | Required | Validation | Description |
|-------|------|----------|------------|-------------|
| `merchant_id` | integer | Yes | min:1 | ID merchant |
| `merchant_name` | string | Yes | max:255 | Tên merchant |
| `representation_name` | string | No | max:255 | Tên đại diện |
| `email` | string | No | max:255, email | Email |
| `mobile` | string | No | max:20 | Số điện thoại |
| `address` | string | No | max:300 | Địa chỉ |
| `passport` | string | No | max:12 | Số CMND/CCCD |

## Business Logic

### Validation Rules
1. **Contract Code Uniqueness**: Các `contract_code` trong cùng một request phải khác nhau
2. **Partner Request ID**: 
   - Bắt buộc với tất cả partner trừ BIDV
   - Chỉ chứa chữ hoa và số
3. **Partner Request Account**: Bắt buộc với partner BIDV
4. **Expired Time**: Phải sau thời gian hiện tại ít nhất 8 giờ

### Processing Logic
1. **Contract Validation**: Kiểm tra hợp đồng có đáp ứng điều kiện thu hộ không
2. **Duplicate Check**: Bỏ qua các yêu cầu đã tồn tại với status != 6
3. **Auto Generate**: Tự động tạo `partner_request_id` nếu không có (format: NL{partner_code}{yymmdd}{id})
4. **Auto Expire**: Tự động set thời gian hết hạn nếu không có (23:00 hôm nay hoặc ngày mai)
5. **Auto Approve**: Tự động duyệt nếu `is_approved = true`

## Response Structure

### Success Response
```json
{
  "success": true,
  "data": {
    "success_partner_request_id": [
      "NLBIDV25080112345"
    ],
    "skipped_partner_request_id": [
      "EXISTING_REQUEST_ID"
    ],
    "total_processed": 1,
    "total_skipped": 0
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error message",
  "errors": {
    "field": ["Validation error message"]
  }
}
```

## Example Request

```bash
curl --location 'https://gateway-dev.nextlend.vn/api-collection/CreateRequestPaymentGuideMultiple' \
--header 'Content-Type: application/json' \
--data-raw '{
  "channel_code": "WEBBACKEND_PARTNER",
  "data": [
    {
      "contract_code": "BIDV-*************-L1",
      "partner_code": "BIDV",
      "partner_contract_code": "BIDV67482357",
      "contract_amount": ********,
      "type": 2,
      "payment_guide": [
        {
          "payment_method_code": "MPOS",
          "payment_channel_code": "MPOS",
          "payment_account_id": "*********",
          "other_data": []
        }
      ],
      "merchant": {
        "merchant_id": "*********",
        "merchant_name": "Ten Merchant",
        "representation_name": "Ten Merchant",
        "email": "<EMAIL>",
        "mobile": "***********",
        "address": "trụ sở 123",
        "passport": "58977"
      },
      "amount": 1000000,
      "partner_request_id": null,
      "created_by": "quantri",
      "requested_by": "quantri",
      "requested_at": "2025-08-01 00:00:00",
      "expired_at": null,
      "is_approved": true,
      "profile_id": 1,
      "is_required_notify": true,
      "partner_request_bank_code": "BIDV",
      "partner_request_holdername": "NGUYEN VAN AN",
      "partner_request_account": "*********"
    }
  ],
  "time_request": **********,
  "version": "1.0",
  "checksum": "932928c308573602ba65416cb1e2f8c0"
}'
```

## Error Codes & Messages

### Common Errors
- **400**: Bad Request - Validation errors
- **401**: Unauthorized - Invalid checksum
- **500**: Internal Server Error

### Business Errors
- "Không có hợp đồng nào đáp ứng được điều kiện thu hộ"
- "Các contract_code phải khác nhau"
- "Tạo lệnh yêu cầu thu hộ không thành công"
- "Tạo merchant không thành công"

## Notes
1. API sử dụng transaction để đảm bảo tính nhất quán dữ liệu
2. Các yêu cầu đã tồn tại sẽ được bỏ qua thay vì báo lỗi
3. Hệ thống tự động tạo `partner_request_id` nếu không được cung cấp
4. Với partner BIDV, `is_required_cashout` sẽ được set = 1 tự động
