<?php

namespace App\Modules\CollectDebt\Resources;

use Illuminate\Support\Str;
use App\Modules\CollectDebt\Model\RequestPayment;
use Illuminate\Http\Resources\Json\ResourceCollection;

class RequestPaymentResourceCollection extends ResourceCollection
{
	public function toArray($request)
	{
		return [
			'data' => $this->collection->map(function (RequestPayment $requestPayment) {
				$can = [];

				$requestPayment->can = [];

				if ($requestPayment->isPaymentViaMpos() && !empty($requestPayment->partner_transaction_id) && !Str::startsWith($requestPayment->partner_transaction_id, 'CUTOFF_ERROR_')) {
					$can[] = 'CHECK_REQUEST_DEBT';
				}

				if ($requestPayment->isPaymentViaMpos() && $requestPayment->isSentedRequest()) {
					$can[] = 'CANCEL_DEBT_REQUEST';
				}

				$requestPayment->can = $can;

				return $requestPayment;
			})->toArray(),
			'meta' => [
				'current_page' => $this->resource->currentPage(),
				'from' => $this->resource->firstItem(),
				'last_page' => $this->resource->lastPage(),
				'per_page' => $this->resource->perPage(),
				'to' => $this->resource->lastItem(),
				'total' => $this->resource->total(),
			],
			'links' => $this->resource->links(),
		];
	}
}
