<!DOCTYPE html>
<html lang="vi">

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>T<PERSON><PERSON> li<PERSON>u API - <PERSON><PERSON> thống <PERSON>hu hộ nợ</title>
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
	<link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" rel="stylesheet">
	<style>
		body {
			font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
			background-color: #f8f9fa;
			font-size: 14px;
			line-height: 1.6;
		}

		.sidebar {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			min-height: 100vh;
			color: white;
			padding: 15px 0;
			position: fixed;
			width: 280px;
			overflow-y: auto;
		}

		.sidebar h3 {
			color: white;
			margin-bottom: 20px;
			text-align: center;
			font-weight: 600;
			font-size: 18px;
		}

		.nav-link {
			color: rgba(255, 255, 255, 0.9) !important;
			border-radius: 6px;
			margin: 3px 10px;
			padding: 8px 15px;
			transition: all 0.3s ease;
			font-size: 13px;
			text-decoration: none;
		}

		.nav-link:hover {
			color: white !important;
			background-color: rgba(255, 255, 255, 0.15);
			transform: translateX(3px);
			text-decoration: none;
		}

		.nav-link.active {
			background-color: rgba(255, 255, 255, 0.25);
			color: white !important;
			font-weight: 600;
		}

		.main-content {
			background: white;
			border-radius: 8px;
			box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
			margin: 15px;
			padding: 25px;
			min-height: calc(100vh - 30px);
			margin-left: 295px;
		}

		.content-header {
			border-bottom: 2px solid #e9ecef;
			padding-bottom: 15px;
			margin-bottom: 25px;
		}

		.content-header h1 {
			color: #2c3e50;
			font-weight: 600;
			margin-bottom: 8px;
			font-size: 24px;
		}

		.content-header p {
			color: #6c757d;
			font-size: 14px;
			margin-bottom: 0;
		}

		.api-section {
			margin-bottom: 30px;
			padding: 20px;
			border-radius: 6px;
			background: #ffffff;
			border: 1px solid #e9ecef;
		}

		.api-section h2 {
			color: #2c3e50;
			font-weight: 600;
			margin-bottom: 15px;
			font-size: 20px;
			border-bottom: 2px solid #f8f9fa;
			padding-bottom: 8px;
		}

		.api-section h3 {
			color: #34495e;
			font-weight: 600;
			margin: 20px 0 10px 0;
			font-size: 16px;
			padding-bottom: 5px;
			border-bottom: 1px solid #f8f9fa;
		}

		.api-section h4 {
			color: #2c3e50;
			font-weight: 600;
			margin: 15px 0 8px 0;
			font-size: 14px;
		}

		.method-badge {
			display: inline-block;
			padding: 3px 8px;
			border-radius: 4px;
			font-size: 11px;
			font-weight: 600;
			text-transform: uppercase;
			margin-right: 8px;
		}

		.method-get {
			background-color: #61affe;
			color: white;
		}

		.method-post {
			background-color: #49cc90;
			color: white;
		}

		.method-put {
			background-color: #fca130;
			color: white;
		}

		.method-delete {
			background-color: #f93e3e;
			color: white;
		}

		.url-endpoint {
			background-color: #f8f9fa;
			padding: 6px 10px;
			border-radius: 4px;
			font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
			font-size: 12px;
			color: #495057;
			border-left: 3px solid #667eea;
		}

		.table {
			border-radius: 6px;
			overflow: hidden;
			box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
			font-size: 13px;
		}

		.table thead th {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: white;
			border: none;
			font-weight: 600;
			font-size: 12px;
			padding: 10px 12px;
		}

		.table tbody tr:hover {
			background-color: #f8f9fa;
		}

		.table tbody td {
			padding: 8px 12px;
			vertical-align: middle;
		}

		.code-block {
			background-color: #f8f9fa;
			color: #495057;
			padding: 15px;
			border-radius: 6px;
			font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
			font-size: 12px;
			line-height: 1.5;
			overflow-x: auto;
			margin: 10px 0;
			border: 1px solid #e9ecef;
		}

		.code-block pre {
			margin: 0;
			background: none;
			border: none;
			padding: 0;
		}

		.code-block code {
			background: none;
			border: none;
			padding: 0;
			color: inherit;
		}

		.status-badge {
			display: inline-block;
			padding: 2px 6px;
			border-radius: 3px;
			font-size: 10px;
			font-weight: 600;
		}

		.status-success {
			background-color: #d4edda;
			color: #155724;
		}

		.status-error {
			background-color: #f8d7da;
			color: #721c24;
		}

		.loading {
			text-align: center;
			padding: 40px;
			color: #6c757d;
		}

		.spinner {
			border: 3px solid #f3f3f3;
			border-top: 3px solid #667eea;
			border-radius: 50%;
			width: 30px;
			height: 30px;
			animation: spin 1s linear infinite;
			margin: 0 auto 15px;
		}

		@keyframes spin {
			0% {
				transform: rotate(0deg);
			}

			100% {
				transform: rotate(360deg);
			}
		}

		.back-to-top {
			position: fixed;
			bottom: 20px;
			right: 20px;
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: white;
			border: none;
			border-radius: 50%;
			width: 40px;
			height: 40px;
			font-size: 16px;
			cursor: pointer;
			box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
			transition: all 0.3s ease;
			display: none;
			z-index: 1000;
		}

		.back-to-top:hover {
			transform: translateY(-2px);
			box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
		}

		/* Markdown content styling */
		.markdown-content {
			font-size: 14px;
			line-height: 1.6;
		}

		.markdown-content h1 {
			font-size: 22px;
			font-weight: 600;
			margin: 25px 0 15px 0;
			color: #2c3e50;
		}

		.markdown-content h2 {
			font-size: 18px;
			font-weight: 600;
			margin: 20px 0 12px 0;
			color: #34495e;
		}

		.markdown-content h3 {
			font-size: 16px;
			font-weight: 600;
			margin: 18px 0 10px 0;
			color: #495057;
		}

		.markdown-content h4 {
			font-size: 14px;
			font-weight: 600;
			margin: 15px 0 8px 0;
			color: #6c757d;
		}

		.markdown-content p {
			margin: 8px 0;
			color: #495057;
		}

		.markdown-content ul,
		.markdown-content ol {
			margin: 8px 0;
			padding-left: 20px;
		}

		.markdown-content li {
			margin: 4px 0;
			color: #495057;
		}

		.markdown-content blockquote {
			border-left: 4px solid #667eea;
			padding-left: 15px;
			margin: 15px 0;
			color: #6c757d;
			font-style: italic;
		}

		.markdown-content strong {
			font-weight: 600;
			color: #2c3e50;
		}

		.markdown-content em {
			font-style: italic;
			color: #6c757d;
		}

		.markdown-content a {
			color: #667eea;
			text-decoration: none;
		}

		.markdown-content a:hover {
			text-decoration: underline;
		}

		@media (max-width: 768px) {
			.sidebar {
				position: relative;
				width: 100%;
				min-height: auto;
			}

			.main-content {
				margin: 10px;
				padding: 15px;
				margin-left: 10px;
			}

			.content-header h1 {
				font-size: 20px;
			}
		}
	</style>
</head>

<body>
	<div class="container-fluid">
		<div class="row">
			<!-- Sidebar -->
			<div class="col-md-3 col-lg-2 sidebar">
				<h3>📚 API Docs</h3>
				<nav class="nav flex-column">
					<a class="nav-link active" href="#overview" onclick="loadContent('README.md', 'overview')">
						🏠 Tổng quan
					</a>
					<a class="nav-link" href="#get-all-request-payment-guide" onclick="loadContent('get-all-request-payment-guide-api.md', 'get-all-request-payment-guide')">
						GetAllRequestPaymentGuide
					</a>
					<a class="nav-link" href="#find-request-payment-guide" onclick="loadContent('find-request-payment-guide-api.md', 'find-request-payment-guide')">
						FindRequestPaymentGuide
					</a>
					<a class="nav-link" href="#create-request-payment-guide-multiple" onclick="loadContent('CreateRequestPaymentGuideMultiple.md', 'create-request-payment-guide-multiple')">
						CreateRequestPaymentGuideMultiple
					</a>
					<div class="mt-3">
						<h6 style="color: rgba(255,255,255,0.7); font-size: 11px; text-transform: uppercase;">
							📁 Tài liệu khác
						</h6>
						<small style="color: rgba(255,255,255,0.6); font-size: 11px;">
							Các API khác sẽ được thêm vào đây
						</small>
					</div>
				</nav>
			</div>

			<!-- Main Content -->
			<div class="col-md-9 col-lg-10">
				<div class="main-content">
					<div class="content-header">
						<h1>🚀 Tài liệu API - Hệ thống Thu hộ nợ</h1>
						<p>Hướng dẫn sử dụng các API của hệ thống thu hộ nợ NextPay</p>
					</div>

					<div id="content">
						<div class="loading">
							<div class="spinner"></div>
							<p>Đang tải tài liệu...</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Back to top button -->
	<button class="back-to-top" onclick="scrollToTop()" title="Lên đầu trang">
		↑
	</button>

	<!-- Scripts -->
	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/marked/4.3.0/marked.min.js"></script>

	<script>
		// Cấu hình marked để render markdown
		marked.setOptions({
			highlight: function (code, lang) {
				if (Prism.languages[lang]) {
					return Prism.highlight(code, Prism.languages[lang], lang);
				}
				return code;
			},
			breaks: true,
			gfm: true
		});

		// Load nội dung mặc định
		document.addEventListener('DOMContentLoaded', function () {
			// Kiểm tra fragment trong URL
			const hash = window.location.hash.substring(1);
			if (hash) {
				loadContentByFragment(hash);
			} else {
				loadContent('README.md', 'overview');
			}
		});

		// Mapping fragment to filename
		const fragmentToFile = {
			'overview': 'README.md',
			'get-all-request-payment-guide': 'get-all-request-payment-guide-api.md',
			'find-request-payment-guide': 'find-request-payment-guide-api.md',
			'create-request-payment-guide-multiple': 'CreateRequestPaymentGuideMultiple.md'
		};

		// Load content by fragment
		function loadContentByFragment(fragment) {
			const filename = fragmentToFile[fragment];
			if (filename) {
				loadContent(filename, fragment);
			} else {
				loadContent('README.md', 'overview');
			}
		}

		// Hàm load nội dung
		async function loadContent(filename, fragment = null) {
			const contentDiv = document.getElementById('content');
			const navLinks = document.querySelectorAll('.nav-link');

			// Hiển thị loading
			contentDiv.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Đang tải ${filename}...</p>
                </div>
            `;

			// Cập nhật URL fragment
			if (fragment) {
				window.history.pushState(null, null, `#${fragment}`);
			}

			// Cập nhật active nav link
			navLinks.forEach(link => link.classList.remove('active'));
			if (fragment) {
				const activeLink = document.querySelector(`a[href="#${fragment}"]`);
				if (activeLink) {
					activeLink.classList.add('active');
				}
			} else if (event && event.target) {
				event.target.classList.add('active');
			}

			try {
				const response = await fetch(filename);
				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				const markdown = await response.text();
				const html = marked.parse(markdown);

				// Thêm styling cho HTML được render
				const styledHtml = addCustomStyling(html);
				contentDiv.innerHTML = `<div class="markdown-content">${styledHtml}</div>`;

				// Highlight code blocks
				Prism.highlightAll();

				// Scroll to top
				window.scrollTo(0, 0);

			} catch (error) {
				contentDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>❌ Lỗi tải tài liệu</h4>
                        <p>Không thể tải file: ${filename}</p>
                        <p><strong>Chi tiết lỗi:</strong> ${error.message}</p>
                    </div>
                `;
			}
		}

		// Thêm styling tùy chỉnh cho HTML được render
		function addCustomStyling(html) {
			return html
				// Thêm class cho các bảng
				.replace(/<table>/g, '<table class="table table-striped">')
				// Thêm class cho code blocks
				.replace(/<pre><code>/g, '<pre class="code-block"><code>');
		}

		// Back to top functionality
		function scrollToTop() {
			window.scrollTo({
				top: 0,
				behavior: 'smooth'
			});
		}

		// Show/hide back to top button
		window.addEventListener('scroll', function () {
			const backToTopBtn = document.querySelector('.back-to-top');
			if (window.pageYOffset > 300) {
				backToTopBtn.style.display = 'block';
			} else {
				backToTopBtn.style.display = 'none';
			}
		});

		// Handle browser back/forward buttons
		window.addEventListener('popstate', function(e) {
			const hash = window.location.hash.substring(1);
			if (hash) {
				loadContentByFragment(hash);
			} else {
				loadContent('README.md', 'overview');
			}
		});

		// Smooth scrolling for anchor links
		document.addEventListener('click', function (e) {
			if (e.target.tagName === 'A' && e.target.getAttribute('href').startsWith('#')) {
				const href = e.target.getAttribute('href').substring(1);

				// Kiểm tra xem có phải là fragment navigation không
				if (fragmentToFile[href]) {
					e.preventDefault();
					loadContentByFragment(href);
					return;
				}

				// Nếu không phải fragment navigation, xử lý như anchor link thông thường
				e.preventDefault();
				const targetElement = document.getElementById(href);
				if (targetElement) {
					targetElement.scrollIntoView({
						behavior: 'smooth',
						block: 'start'
					});
				}
			}
		});
	</script>
</body>

</html>